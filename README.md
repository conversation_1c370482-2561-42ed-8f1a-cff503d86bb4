# CODA Backend

[![pipeline status](http://************/alexandros/coda/badges/dev/pipeline.svg)](http://************/alexandros/coda/-/commits/dev)
[![coverage report](http://************/alexandros/coda/badges/dev/coverage.svg)](http://************/alexandros/coda/-/commits/dev)

## Overview

CODA (Communications Oversight and Disposition Assurance) is a distributed, microservices-based system built on Django that provides real-time audio transcription, natural language processing, and content analysis capabilities. The system is designed to handle high-volume audio processing workloads with GPU acceleration and horizontal scaling.

### Key Features

- **Real-time Audio Transcription**: GPU-accelerated speech-to-text processing using WhisperX
- **LLM-powered Analysis**: Content summarization and red flag detection using transformer models
- **Distributed Processing**: Celery-based task queue system with RabbitMQ
- **Multi-tenant Architecture**: Support for multiple customers and tenants
- **RESTful API**: Comprehensive API for integration and monitoring
- **Docker Support**: Full containerization with GPU support
- **Monitoring**: Built-in health checks and Celery Flower integration

## Architecture

CODA follows an asynchronous microservices architecture with the following core components:

### **System Components**

#### **Messenger System**
- **Purpose**: Message ingestion and workflow orchestration
- **Functionality**:
  - Consumes messages from RabbitMQ queues
  - Validates incoming requests and service parameters
  - Creates and distributes tasks to appropriate workers
  - Implements rate limiting and error handling
- **Location**: `src/worker/management/commands/messenger.py`

#### **Admin System**
- **Purpose**: Web-based management interface
- **Functionality**:
  - Django admin interface for system configuration
  - Service configuration management
  - Worker monitoring and control
  - System health monitoring
- **Access**: Web interface and CLI commands

#### **Transcription GPU Workers**
- **Purpose**: Audio-to-text conversion using GPU acceleration
- **Technology**: WhisperX with CUDA support
- **Queue**: `transcription`
- **Functionality**:
  - Downloads audio files from S3
  - Performs speech-to-text transcription
  - Supports multiple languages (primarily English)
  - Uploads results back to tenant databases

#### **LLM GPU Workers**
- **Purpose**: Natural language processing and analysis
- **Technology**: Transformer models with GPU acceleration
- **Queues**: `summary`, `redflag`
- **Functionality**:
  - Content summarization
  - Red flag detection and content analysis
  - Configurable prompt templates
  - Support for both local and remote API models

#### **CPU Workers**
- **Purpose**: General-purpose task processing
- **Queue**: `default`
- **Functionality**:
  - Non-GPU intensive tasks
  - Database operations
  - File management
  - Integration tasks

#### **Monitoring System**
- **Purpose**: System health and task monitoring
- **Technology**: Celery Flower
- **Functionality**:
  - Real-time worker monitoring
  - Task queue visualization
  - Performance metrics
  - Worker management interface

## User Interfaces
- [User Interfaces Overview](./docs/user_interfaces_overview.md) - Complete guide to all interfaces
- [Admin Interface Guide](./docs/admin_interface_guide.md) - Web-based administration panel
- [CLI Interface Guide](./docs/cli_interface_guide.md) - Command-line tools and scripts
- [API Interface Guide](./docs/api_interface_guide.md) - RESTful API endpoints

## Quick Start

### Prerequisites
- Python 3.12+
- Docker and Docker Compose
- NVIDIA GPU with CUDA support (for GPU workers)
- RabbitMQ
- MySQL/MariaDB

### Using Docker (Recommended)
```bash
# Clone the repository
<NAME_EMAIL>:alex/coda.git
cd coda

# Set up environment variables
cp .env-compose.example .env-compose
# Edit .env-compose with your configuration

# Start the system
docker compose up -d

# Create Django superuser
docker exec -it admin python manage.py createsuperuser
```

### Manual Installation
See [Installation Guide](./docs/installation.md) for detailed manual setup instructions.

## Table of Contents

### 📚 Documentation
- [🏗️ Architecture Overview](./docs/architecture.md)
- [🚀 Installation Guide](./docs/installation.md)
- [🐳 Docker Setup](./docs/docker.md)
- [👩‍💻 Developer Guide](./docs/dev_guide.md)
- [🔧 Configuration Guide](./docs/configuration.md)
- [📊 API Documentation](./docs/api.md)
- [🖥️ NVIDIA/CUDA Setup](./docs/cuda.md)

### 🔧 Operations
- [🚀 Deployment Guide](./docs/deployment.md)
- [📈 Monitoring & Health Checks](./docs/monitoring.md)
- [🐛 Troubleshooting](./docs/troubleshooting.md)

### 📋 Reference
- [🔗 API Reference](#api-endpoints)
- [📊 System Diagrams](#system-diagrams)
- [🧪 Testing Guide](./docs/testing.md)

## System Diagrams

### High-Level Architecture
The system follows a distributed microservices pattern with message queues:

![High-Level Architecture](docs/coda_hld_architecture_basic_message_flow.png)

### Low-Level Component Diagram
Detailed view of component interactions and data flow:

![Low-Level Architecture](docs/coda_lld_architecture.png)

### Data Flow Diagram

![Data Flow Diagram](docs/data_flow_diagram.png)

## API Endpoints

The CODA API provides RESTful endpoints for system interaction:

- **Base URL**: `http://localhost:8000/codaapi/`
- **API Documentation**: `http://localhost:8000/codaapi/docs/` (Swagger UI)
- **Health Check**: `http://localhost:8000/codaapi/health/`

### Key Endpoints
- `GET /health/` - System health status
- `GET /health/workers/` - Worker status monitoring
- `GET /workflow-jobs/` - Workflow job management
- `GET /service-jobs/` - Service job monitoring
- `POST /inference-jobs/` - LLM inference requests

## Technology Stack

### Core Framework
- **Django 5.2**: Web framework and ORM
- **Django REST Framework**: API development
- **Celery 5.5**: Distributed task queue
- **RabbitMQ**: Message broker

### AI/ML Components
- **WhisperX 3.3**: Speech-to-text transcription
- **Transformers 4.51**: LLM processing
- **PyTorch 2.7**: Deep learning framework
- **CUDA**: GPU acceleration

### Infrastructure
- **Docker**: Containerization
- **MySQL**: Primary database
- **Redis**: Caching and session storage
- **NGINX**: Reverse proxy and load balancing

## Environment Variables

Key environment variables for configuration:

```bash
# Django Configuration
DJANGO_SETTINGS_MODULE=coda.settings
SECRET_KEY=your-secret-key
LOG_LEVEL=INFO

# Database Configuration
CODA_DB_HOST=localhost
CODA_DB_PORT=3306
CODA_DB_NAME=coda_db
CODA_DB_USER=coda_user
CODA_DB_PASSWORD=your-password

# Message Queue
CELERY_BROKER_URL=amqp://user:pass@rabbitmq:5672//
RABBITMQ_URL=amqp://user:pass@rabbitmq:5672//messenger

# Worker Configuration
WORKER_TYPE=APP  # Options: APP, TRANSCRIPTION, GPU24

# AWS Configuration (for S3 storage)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
```

## Development

### Local Development Setup

```bash
# Clone and setup
<NAME_EMAIL>:alex/coda.git
cd coda

# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Setup database
cd src
python manage.py migrate
python manage.py createsuperuser

# Start development server
python manage.py runserver
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test categories
pytest -m unit
pytest -m integration
```

### Code Quality

```bash
# Format code
black src/
isort src/

# Check style
flake8 src/
mypy src/

# Run pre-commit hooks
pre-commit run --all-files
```

## Production Deployment

### Docker Deployment

```bash
# Build images
docker build -t coda:latest .
docker build --target transcription -t coda-transcription:latest .
docker build --target llm_generic -t coda-llm:latest .

# Deploy with docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

## Monitoring and Health

### Health Checks

- **System Health**: `GET /codaapi/health/`
- **Worker Status**: `GET /codaapi/health/workers/`
- **Flower Dashboard**: `http://localhost:5555`

### Key Metrics

- Queue depths and processing rates
- Worker health and GPU utilization
- API response times and error rates
- Database performance and connections

## Support and Contributing

### Getting Help

- 📖 **Documentation**: Check the [docs/](./docs/) directory
- 🐛 **Bug Reports**: Slack ai-coda-general channel
- 💬 **Questions**: Slack ai-coda-general channel
- 🚀 **Feature Requests**: Slack ai-coda-general channel

### Contributing

1. Read the [Developer Guide](./docs/dev_guide.md)
2. Fork the repository and create a feature branch
3. Make your changes with tests and documentation
4. Submit a merge request for review

### Development Team

- **Architecture**: System design and technical decisions
- **Backend**: Django, Celery, and API development
- **ML/AI**: Transcription and LLM model integration
- **DevOps**: Infrastructure, deployment, and monitoring

## License

This project is proprietary software. All rights reserved.

---

**CODA** - Communications Oversight and Disposition Assurance
Built with ❤️ by the Umony Development Team
