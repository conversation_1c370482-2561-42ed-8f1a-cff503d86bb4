# CODA Monitoring & Health Checks

## Overview

CODA provides comprehensive monitoring capabilities through built-in health checks, metrics collection, and integration with popular monitoring tools. This guide covers monitoring setup, key metrics, alerting, and troubleshooting procedures.

## Built-in Health Checks

### System Health Endpoint

**URL**: `GET /codaapi/health/`

**Purpose**: Comprehensive system health check covering all critical components.

**Response Format**:
```json
{
  "status": "ok",
  "database": "ok",
  "rabbitmq": "ok",
  "consumers": "ok",
  "timestamp": "2024-06-18T10:30:00Z",
  "details": {
    "database_latency_ms": 5.2,
    "rabbitmq_connection": "active",
    "active_consumers": 8,
    "queue_depths": {
      "transcription": 12,
      "summary": 3,
      "redflag": 1,
      "default": 45
    }
  }
}
```

**Health Status Values**:
- `ok` - All systems operational
- `degraded` - Some issues but system functional
- `error` - Critical issues affecting functionality
- `unavailable` - System not accessible

### Worker Health Endpoint

**URL**: `GET /codaapi/health/workers/`

**Purpose**: Detailed status of all worker nodes.

**Response Format**:
```json
[
  {
    "id": 1,
    "name": "transcription-worker-01",
    "hostname": "gpu-node-01",
    "ec2_type": "g4dn.xlarge",
    "status": "running",
    "healthy": true,
    "down_for_maintenance": false,
    "ping_response": "ok",
    "last_seen": "2024-06-18T10:29:45Z",
    "metrics": {
      "cpu_usage": 45.2,
      "memory_usage": 78.5,
      "gpu_memory_usage": 85.3,
      "active_tasks": 2,
      "completed_tasks_24h": 156
    }
  }
]
```

## Key Metrics to Monitor

### System-Level Metrics

#### Application Performance
- **Response Time**: API endpoint response times
- **Throughput**: Requests per second
- **Error Rate**: 4xx/5xx error percentage
- **Uptime**: Service availability percentage

#### Queue Metrics
- **Queue Depth**: Number of pending tasks per queue
- **Processing Rate**: Tasks processed per minute
- **Wait Time**: Average time tasks spend in queue
- **Failed Tasks**: Number of failed tasks per hour

#### Worker Metrics
- **Worker Utilization**: Percentage of workers actively processing
- **Task Duration**: Average time to complete tasks
- **Memory Usage**: RAM and GPU memory consumption
- **Worker Health**: Number of healthy vs unhealthy workers

### Business Metrics

#### Transcription Service
- **Audio Processing Rate**: Hours of audio processed per hour
- **Transcription Accuracy**: Quality metrics (if available)
- **Language Distribution**: Breakdown by detected language
- **File Size Distribution**: Audio file size statistics

#### LLM Services
- **Summary Generation Rate**: Summaries generated per hour
- **Red Flag Detection Rate**: Percentage of content flagged
- **Model Performance**: Inference time and quality metrics
- **Token Usage**: Input/output token consumption

## Monitoring Tools Integration

### Prometheus Metrics

CODA can expose metrics in Prometheus format. Add to your Django settings:

```python
# settings.py
INSTALLED_APPS += ['django_prometheus']
MIDDLEWARE = ['django_prometheus.middleware.PrometheusBeforeMiddleware'] + MIDDLEWARE + ['django_prometheus.middleware.PrometheusAfterMiddleware']
```

**Key Prometheus Metrics**:
```
# HTTP metrics
django_http_requests_total{method="GET",status="200"}
django_http_request_duration_seconds

# Database metrics
django_db_connections_total
django_db_execute_total

# Celery metrics
celery_tasks_total{queue="transcription",status="success"}
celery_task_duration_seconds{queue="transcription"}
celery_workers_total{status="online"}
```

### Grafana Dashboard

Example Grafana dashboard configuration:

```json
{
  "dashboard": {
    "title": "CODA System Overview",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(django_http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Queue Depths",
        "type": "graph",
        "targets": [
          {
            "expr": "celery_queue_length",
            "legendFormat": "{{queue}}"
          }
        ]
      },
      {
        "title": "Worker Status",
        "type": "stat",
        "targets": [
          {
            "expr": "celery_workers_total{status=\"online\"}",
            "legendFormat": "Online Workers"
          }
        ]
      }
    ]
  }
}
```

### ELK Stack Integration

#### Filebeat Configuration

```yaml
# filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
  - add_docker_metadata:
      host: "unix:///var/run/docker.sock"
  - decode_json_fields:
      fields: ["message"]
      target: ""

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "coda-logs-%{+yyyy.MM.dd}"

setup.template.settings:
  index.number_of_shards: 1
  index.codec: best_compression
```

#### Logstash Pipeline

```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [container][name] =~ /coda/ {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{DATA:logger} %{GREEDYDATA:message}" }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    if [level] == "ERROR" {
      mutate {
        add_tag => [ "error" ]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "coda-logs-%{+YYYY.MM.dd}"
  }
}
```

## Alerting Rules

### Critical Alerts

#### System Down
```yaml
# prometheus-alerts.yml
groups:
- name: coda-critical
  rules:
  - alert: CODASystemDown
    expr: up{job="coda-web"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "CODA system is down"
      description: "CODA web service has been down for more than 1 minute"
```

#### High Error Rate
```yaml
  - alert: HighErrorRate
    expr: rate(django_http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"
```

#### Queue Backlog
```yaml
  - alert: QueueBacklog
    expr: celery_queue_length > 1000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Queue backlog detected"
      description: "Queue {{ $labels.queue }} has {{ $value }} pending tasks"
```

#### GPU Memory High
```yaml
  - alert: GPUMemoryHigh
    expr: nvidia_gpu_memory_used_bytes / nvidia_gpu_memory_total_bytes > 0.9
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "GPU memory usage high"
      description: "GPU memory usage is {{ $value | humanizePercentage }}"
```

### Warning Alerts

#### Worker Offline
```yaml
  - alert: WorkerOffline
    expr: celery_workers_total{status="online"} < 5
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "Worker count low"
      description: "Only {{ $value }} workers are online"
```

#### Slow Response Time
```yaml
  - alert: SlowResponseTime
    expr: histogram_quantile(0.95, rate(django_http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Slow response times"
      description: "95th percentile response time is {{ $value }}s"
```

## Log Analysis

### Structured Logging Format

CODA uses structured JSON logging for better analysis:

```json
{
  "timestamp": "2024-06-18T10:30:00.123Z",
  "level": "INFO",
  "logger": "transcription.services",
  "message": "Transcription completed",
  "extra": {
    "parley_id": 456789,
    "customer_id": 100069,
    "duration_seconds": 45.2,
    "audio_length_seconds": 180,
    "model_used": "large-v3",
    "language_detected": "en"
  }
}
```

### Common Log Patterns

#### Error Patterns
```bash
# Find GPU memory errors
grep "CUDA out of memory" /var/log/coda/*.log

# Find database connection errors
grep "database.*connection.*failed" /var/log/coda/*.log

# Find task failures
grep "task.*failed" /var/log/coda/*.log
```

#### Performance Analysis
```bash
# Find slow transcriptions
jq 'select(.extra.duration_seconds > 60)' /var/log/coda/transcription.log

# Find large audio files
jq 'select(.extra.audio_length_seconds > 3600)' /var/log/coda/transcription.log

# Analyze error distribution
jq -r '.level' /var/log/coda/*.log | sort | uniq -c
```

## Performance Monitoring

### Database Performance

#### Slow Query Monitoring
```sql
-- Enable slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = 'ON';

-- Monitor active connections
SHOW PROCESSLIST;

-- Check table locks
SHOW OPEN TABLES WHERE In_use > 0;
```

#### Key Database Metrics
- **Connection Pool Usage**: Active vs available connections
- **Query Response Time**: Average and 95th percentile
- **Lock Wait Time**: Time spent waiting for locks
- **Replication Lag**: Delay in read replicas (if used)

### GPU Monitoring

#### NVIDIA GPU Metrics
```bash
# Monitor GPU usage
nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv --loop=1

# Monitor GPU processes
nvidia-smi pmon

# Check GPU temperature
nvidia-smi --query-gpu=temperature.gpu --format=csv
```

#### GPU Performance Alerts
- **GPU Utilization**: Should be >80% during active processing
- **Memory Usage**: Alert when >90% to prevent OOM errors
- **Temperature**: Alert when >80°C
- **Power Usage**: Monitor for hardware issues

### Memory Monitoring

#### System Memory
```bash
# Monitor memory usage
free -h
cat /proc/meminfo

# Monitor swap usage
swapon --show

# Check for memory leaks
ps aux --sort=-%mem | head -10
```

#### Application Memory
```bash
# Monitor Python memory usage
docker exec coda-web python -c "
import psutil
process = psutil.Process()
print(f'Memory: {process.memory_info().rss / 1024 / 1024:.2f} MB')
"

# Monitor GPU memory
docker exec transcription-worker nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits
```

## Health Check Automation

### Kubernetes Health Checks

```yaml
# Liveness probe
livenessProbe:
  httpGet:
    path: /codaapi/health/
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Readiness probe
readinessProbe:
  httpGet:
    path: /codaapi/health/
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 2
```

### Custom Health Check Script

```bash
#!/bin/bash
# health-check.sh

set -e

BASE_URL="http://localhost:8000/codaapi"

# Check system health
HEALTH_RESPONSE=$(curl -s "$BASE_URL/health/" | jq -r '.status')
if [ "$HEALTH_RESPONSE" != "ok" ]; then
    echo "CRITICAL: System health check failed: $HEALTH_RESPONSE"
    exit 2
fi

# Check worker health
UNHEALTHY_WORKERS=$(curl -s "$BASE_URL/health/workers/" | jq '[.[] | select(.healthy == false)] | length')
if [ "$UNHEALTHY_WORKERS" -gt 0 ]; then
    echo "WARNING: $UNHEALTHY_WORKERS unhealthy workers detected"
    exit 1
fi

# Check queue depths
QUEUE_DEPTH=$(curl -s "$BASE_URL/health/" | jq '.details.queue_depths.transcription')
if [ "$QUEUE_DEPTH" -gt 100 ]; then
    echo "WARNING: High queue depth: $QUEUE_DEPTH"
    exit 1
fi

echo "OK: All health checks passed"
exit 0
```

## Troubleshooting Runbook

### Common Issues and Solutions

#### High Queue Backlog
1. **Check worker status**: Verify workers are online and healthy
2. **Scale workers**: Add more worker instances
3. **Check resource usage**: Ensure adequate CPU/GPU/memory
4. **Review task failures**: Check for systematic failures

#### GPU Out of Memory
1. **Reduce batch size**: Lower transcription batch size
2. **Clear GPU cache**: Restart GPU workers
3. **Check model size**: Verify model fits in available VRAM
4. **Monitor concurrent tasks**: Limit concurrent GPU tasks

#### Database Connection Issues
1. **Check connection pool**: Verify pool settings
2. **Monitor active connections**: Look for connection leaks
3. **Check database health**: Verify database server status
4. **Review slow queries**: Optimize problematic queries

#### RabbitMQ Issues
1. **Check disk space**: RabbitMQ needs adequate disk space
2. **Monitor memory usage**: RabbitMQ memory consumption
3. **Check queue permissions**: Verify user permissions
4. **Review connection limits**: Check connection pool settings

### Emergency Procedures

#### System Recovery
```bash
# 1. Stop all workers
docker-compose stop cpu-worker transcription-worker llm-worker

# 2. Clear problematic queues (if needed)
docker exec rabbitmq rabbitmqctl purge_queue transcription

# 3. Restart services in order
docker-compose up -d db rabbitmq
sleep 30
docker-compose up -d web messenger
sleep 10
docker-compose up -d cpu-worker transcription-worker llm-worker
```

#### Database Recovery
```bash
# 1. Check database status
docker exec db mysqladmin status

# 2. Repair tables if needed
docker exec db mysqlcheck --repair --all-databases

# 3. Restore from backup if necessary
docker exec -i db mysql < /backups/latest_backup.sql
```

## Monitoring Best Practices

### 1. Establish Baselines
- Monitor normal operating patterns
- Document typical resource usage
- Establish performance benchmarks

### 2. Implement Layered Monitoring
- **Infrastructure**: CPU, memory, disk, network
- **Application**: Response times, error rates, throughput
- **Business**: Task completion rates, quality metrics

### 3. Set Appropriate Thresholds
- **Critical**: Immediate action required
- **Warning**: Investigation needed
- **Info**: Awareness only

### 4. Regular Review
- Weekly performance reviews
- Monthly capacity planning
- Quarterly monitoring strategy updates

### 5. Documentation
- Maintain runbooks for common issues
- Document escalation procedures
- Keep monitoring configuration in version control
