# CODA Configuration Guide

## Overview

CODA uses a multi-layered configuration system that combines environment variables, Django settings, and database-stored configurations. This guide covers all configuration aspects from basic setup to advanced service tuning.

## Environment Configuration

### Core Django Settings

```bash
# Django Framework
DJANGO_SETTINGS_MODULE=coda.settings.production  # or .development, .test
SECRET_KEY=your-super-secret-key-here
DEBUG=False
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# Host Configuration
HOSTNAME=your-hostname
HOST_IP=your-host-ip
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
```

### Database Configuration

#### Primary CODA Database
```bash
CODA_DB_NAME=coda_production
CODA_DB_USER=coda_user
CODA_DB_PASSWORD=secure_password
CODA_DB_HOST=mysql-server
CODA_DB_PORT=3306
```

#### Tenant Estate Database
```bash
TE_DB_NAME=tranquility_estate
TE_DB_USER=estate_user
TE_DB_PASSWORD=estate_password
TE_DB_HOST=mysql-server
TE_DB_PORT=3306
```

### Message Queue Configuration

```bash
# RabbitMQ Settings
CELERY_BROKER_URL=amqps://username:password@rabbitmq-host:5671//
RABBITMQ_URL=amqps://username:password@rabbitmq-host:5671//messenger

# Queue Names (optional overrides)
MESSENGER_QUEUE=messenger
```

### Worker Configuration

```bash
# Worker Type - determines which services this instance runs
WORKER_TYPE=APP          # Options: APP, TRANSCRIPTION, GPU24

# For GPU workers
CT2_CUDA_ALLOW_FP16=1   # Enable FP16 for faster inference
```

### AWS Configuration

```bash
# AWS Credentials and Region
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# S3 Configuration
S3_BUCKET_NAME=your-audio-bucket
S3_REGION=us-east-1
```

### External Services

```bash
# CKS (Customer Knowledge Service) URLs
CKS_SERVER_URL_1=http://cks-server-1/
CKS_SERVER_URL_2=http://cks-server-2/

# Flower Monitoring
FLOWER_UNAUTHENTICATED_API=true  # For development only
FLOWER_PORT=8001
```

## Service Configuration

CODA uses database-stored configurations for dynamic service management. These can be managed through the Django admin interface.

### Service Configuration Model

Each service (transcription, summary, redflag) has a `ServiceConfiguration` object with the following structure:

```python
class ServiceConfiguration:
    name = "transcription_default"
    description = "Default transcription configuration"
    enabled = True
    
    # Service-specific settings stored as JSON
    transcription_settings = {
        "whisper_settings": {
            "model_name": "large-v3",
            "device": "cuda",
            "compute_type": "float16"
        },
        "batch_size": 16,
        "language_detection": True
    }
    
    llm_settings = {
        "model_name": "meta-llama/Llama-2-7b-chat-hf",
        "max_length": 512,
        "temperature": 0.7,
        "top_p": 0.9
    }
```

### Transcription Service Configuration

#### WhisperX Model Settings

```json
{
  "whisper_settings": {
    "model_name": "large-v3",           // Model size: tiny, base, small, medium, large, large-v2, large-v3
    "device": "cuda",                   // Device: cuda, cpu
    "compute_type": "float16",          // Precision: float16, float32, int8
    "language": "en",                   // Language code or null for auto-detection
    "task": "transcribe"                // Task: transcribe, translate
  },
  "batch_size": 16,                     // Batch size for processing
  "chunk_length": 30,                   // Audio chunk length in seconds
  "language_detection": true,           // Enable automatic language detection
  "supported_languages": ["en", "es", "fr"],  // Allowed languages
  "audio_processing": {
    "sample_rate": 16000,               // Target sample rate
    "normalize": true,                  // Normalize audio levels
    "remove_silence": false             // Remove silent segments
  }
}
```

#### Performance Tuning

```json
{
  "performance": {
    "max_concurrent_jobs": 2,           // Max simultaneous transcriptions per worker
    "memory_threshold": 0.8,            // GPU memory usage threshold
    "model_cache_size": 2,              // Number of models to keep in memory
    "prefetch_audio": true              // Pre-download audio files
  }
}
```

### LLM Service Configuration

#### Model Configuration

```json
{
  "model_settings": {
    "model_name": "meta-llama/Llama-2-7b-chat-hf",
    "model_type": "local",              // local, remote_api
    "device": "cuda",
    "torch_dtype": "float16",
    "load_in_8bit": false,
    "load_in_4bit": false,
    "trust_remote_code": false
  },
  "generation_config": {
    "max_length": 512,
    "max_new_tokens": 256,
    "temperature": 0.7,
    "top_p": 0.9,
    "top_k": 50,
    "repetition_penalty": 1.1,
    "do_sample": true,
    "pad_token_id": 0,
    "eos_token_id": 2
  }
}
```

#### Remote API Configuration

```json
{
  "remote_api_settings": {
    "api_url": "https://api.openai.com/v1/chat/completions",
    "model_name": "gpt-3.5-turbo",
    "api_key": "your-api-key",
    "timeout": 30,
    "max_retries": 3,
    "headers": {
      "User-Agent": "CODA/1.0"
    }
  }
}
```

#### Prompt Templates

```json
{
  "prompt_templates": {
    "summary": {
      "version": 3,
      "prompt": [
        {
          "role": "system",
          "content": "You are a helpful assistant that summarizes conversations."
        },
        {
          "role": "user",
          "content": "Please summarize the following conversation:\n\n{DIALOGUE_PLACEHOLDER}\n\nContext: {CONTEXT_PLACEHOLDER}"
        }
      ]
    },
    "redflag": {
      "version": 3,
      "prompt": [
        {
          "role": "system",
          "content": "You are an AI assistant that identifies potential issues in customer service conversations."
        },
        {
          "role": "user",
          "content": "Analyze this conversation for red flags:\n\n{DIALOGUE_PLACEHOLDER}"
        }
      ]
    }
  }
}
```

## Worker Configuration

### Worker Host Management

Worker hosts are managed through the `WorkerHost` model:

```python
class WorkerHost:
    name = "transcription-worker-01"
    hostname = "gpu-node-01.internal"
    ec2_type = "g4dn.xlarge"
    status = "running"  # running, stopped, loading
    down_for_maintenance = False
    service_configuration_bypass = None  # Override default config
```

### Celery Worker Configuration

#### Transcription Workers

```bash
# Start transcription worker
celery -A coda.celery worker \
  --loglevel=INFO \
  --concurrency=1 \
  --queues=transcription \
  --max-tasks-per-child=100 \
  --hostname=transcription-worker-01
```

#### LLM Workers

```bash
# Start summary/redflag worker
celery -A coda.celery worker \
  --loglevel=INFO \
  --concurrency=1 \
  --queues=summary,redflag \
  --max-tasks-per-child=50 \
  --hostname=llm-worker-01
```

#### CPU Workers

```bash
# Start general purpose worker
celery -A coda.celery worker \
  --loglevel=INFO \
  --concurrency=4 \
  --queues=default \
  --max-tasks-per-child=1000 \
  --hostname=cpu-worker-01
```

## Messenger Configuration

The messenger system has its own configuration model for runtime behavior:

```python
class MessengerConfiguration:
    name = "default_messenger"
    is_enabled = True
    sleep_seconds = 3                    # Sleep between message checks
    incremental_sleep = True             # Increase sleep when no messages
    incremental_seconds_max = 10         # Max sleep time
    
    # Rate Limiting
    rate_limit_enabled = True
    rate_limit_max_messages = 100        # Max messages per time window
    rate_limit_time_window = 60          # Time window in seconds
    rate_limit_delay_seconds = 30        # Delay when rate limit exceeded
    
    # Publishing
    index_publish_enabled = True         # Enable result publishing
    
    # Logging
    log_level = 20                       # 10=DEBUG, 20=INFO, 30=WARNING, 40=ERROR
```

## Monitoring Configuration

### Health Check Configuration

```python
# Health check intervals and thresholds
HEALTH_CHECK_CONFIG = {
    "database_timeout": 5,               # Database connection timeout
    "rabbitmq_timeout": 5,               # RabbitMQ connection timeout
    "worker_ping_timeout": 10,           # Worker ping timeout
    "unhealthy_threshold": 3,            # Failed checks before marking unhealthy
}
```

### Logging Configuration

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'json': {
            'format': '{"level": "{levelname}", "time": "{asctime}", "module": "{module}", "message": "{message}"}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/coda/coda.log',
            'formatter': 'json',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
}
```

## Security Configuration

### Django Security Settings

```bash
# Security
SECRET_KEY=your-very-long-secret-key-here
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY
```

### Database Security

```bash
# Use SSL for database connections
CODA_DB_OPTIONS='{"ssl": {"ssl_ca": "/path/to/ca.pem"}}'
```

### API Security

```bash
# CORS settings
CORS_ALLOWED_ORIGINS=https://your-frontend.com,https://admin.your-domain.com
CORS_ALLOW_CREDENTIALS=True
```

## Performance Configuration

### Database Optimization

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
            'autocommit': True,
        },
        'CONN_MAX_AGE': 3600,  # Connection pooling
    }
}
```

### Celery Optimization

```python
# Celery performance settings
CELERY_TASK_ACKS_LATE = True
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_WORKER_PROC_ALIVE_TIMEOUT = 1800  # 30 minutes for model loading
```

## Environment-Specific Configurations

### Development Environment

```bash
# .env.development
DEBUG=True
LOG_LEVEL=DEBUG
CELERY_TASK_ALWAYS_EAGER=True  # Run tasks synchronously for testing
LOCAL_TESTING=True
```

### Production Environment

```bash
# .env.production
DEBUG=False
LOG_LEVEL=INFO
SECURE_SSL_REDIRECT=True
CELERY_BROKER_USE_SSL=True
```

### Testing Environment

```bash
# .env.test
DJANGO_SETTINGS_MODULE=coda.settings.test
CELERY_TASK_ALWAYS_EAGER=True
DATABASE_URL=sqlite:///test.db
```

## Configuration Management Best Practices

### 1. Environment Variables
- Use environment variables for sensitive data (passwords, API keys)
- Use `.env` files for local development
- Use container orchestration secrets for production

### 2. Database Configuration
- Store non-sensitive, dynamic configuration in the database
- Use Django admin for configuration management
- Implement configuration validation

### 3. Version Control
- Never commit sensitive configuration to version control
- Use `.env.example` files to document required variables
- Use configuration templates for deployment

### 4. Configuration Validation
- Validate configuration on startup
- Provide clear error messages for invalid configuration
- Use type hints and validation libraries

### 5. Hot Reloading
- Design services to reload configuration without restart
- Use database signals for configuration changes
- Implement graceful configuration updates
