# CODA Testing Guide

## Overview

This guide covers testing strategies, test setup, and best practices for the CODA system. The testing approach includes unit tests, integration tests, performance tests, and end-to-end testing.

## Testing Framework

CODA uses pytest with Django integration for comprehensive testing:

- **pytest-django**: Django-specific testing utilities
- **pytest-cov**: Code coverage reporting
- **factory-boy**: Test data generation
- **mock**: Mocking external dependencies
- **celery**: Task testing utilities

## Test Configuration

### Test Settings

Create `src/coda/settings/test.py`:

```python
from .base import *

# Test database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Test-specific settings
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Disable external services
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Fast password hashing for tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Disable logging during tests
LOGGING_CONFIG = None
```

### pytest Configuration

Create `pytest.ini`:

```ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = coda.settings.test
python_files = test_*.py *_test.py testing/python/*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=.
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    gpu: Tests requiring GPU
    external: Tests requiring external services
```

### Test Dependencies

Add to `requirements-dev.txt`:

```txt
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
factory-boy==3.3.0
freezegun==1.2.2
responses==0.24.1
```

## Unit Testing

### Model Tests

```python
# tests/test_models.py
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from worker.models import WorkerHost, ServiceJob, WorkflowJob
from service.models import ServiceConfiguration


class TestWorkerHost(TestCase):
    def setUp(self):
        self.worker_host = WorkerHost.objects.create(
            name="test-worker-01",
            hostname="test-host",
            ec2_type="g4dn.xlarge"
        )

    def test_worker_host_creation(self):
        """Test worker host creation with valid data."""
        self.assertEqual(self.worker_host.name, "test-worker-01")
        self.assertEqual(self.worker_host.status, "stopped")
        self.assertFalse(self.worker_host.running)

    def test_worker_host_running_property(self):
        """Test running property based on status."""
        self.worker_host.status = "running"
        self.worker_host.save()
        self.assertTrue(self.worker_host.running)

    def test_unique_name_constraint(self):
        """Test that worker host names must be unique."""
        with self.assertRaises(Exception):
            WorkerHost.objects.create(
                name="test-worker-01",  # Duplicate name
                hostname="another-host",
                ec2_type="t3.medium"
            )


class TestServiceJob(TestCase):
    def setUp(self):
        self.workflow_job = WorkflowJob.objects.create(
            parley_id=123456,
            customer_id=100069,
            language="en",
            status="pending"
        )
        
        self.service_job = ServiceJob.objects.create(
            workflow_job=self.workflow_job,
            service_type="transcription",
            parley_id=123456,
            customer_id=100069,
            language="en"
        )

    def test_service_job_start(self):
        """Test starting a service job."""
        self.service_job.start()
        self.assertEqual(self.service_job.status, "started")
        self.assertIsNotNone(self.service_job.started_at)

    def test_service_job_completion(self):
        """Test completing a service job."""
        self.service_job.start()
        self.service_job.mark_completed()
        self.assertEqual(self.service_job.status, "completed")
        self.assertIsNotNone(self.service_job.completed_at)

    def test_service_job_failure(self):
        """Test failing a service job."""
        error_message = "Test error"
        self.service_job.mark_failed(error_message)
        self.assertEqual(self.service_job.status, "failed")
        self.assertEqual(self.service_job.error, error_message)
        self.assertIsNotNone(self.service_job.failed_at)
```

### Service Tests

```python
# tests/test_services.py
import pytest
from unittest.mock import Mock, patch, MagicMock
from transcription.services import TranscriptionService, TranscriptionModelService
from llm_model.services import LLMModelService
from service.models import ServiceConfiguration


class TestTranscriptionService:
    @pytest.fixture
    def mock_configuration(self):
        config = Mock(spec=ServiceConfiguration)
        config.transcription_settings = Mock()
        config.transcription_settings.whisper_settings = {
            "model_name": "large-v3",
            "device": "cuda",
            "compute_type": "float16"
        }
        config.transcription_settings.batch_size = 16
        return config

    @pytest.fixture
    def transcription_service(self, mock_configuration):
        with patch('transcription.services.TranscriptionModelService'):
            service = TranscriptionService(mock_configuration)
            return service

    @patch('transcription.services.S3Client')
    @patch('transcription.services.TenantClient')
    def test_transcribe_success(self, mock_tenant_client, mock_s3_client, transcription_service):
        """Test successful transcription workflow."""
        # Mock S3 download
        mock_s3_client.return_value.download_file.return_value = "/tmp/test.wav"
        
        # Mock transcription result
        mock_result = {
            "segments": [
                {"start": 0.0, "end": 5.0, "text": "Hello world"}
            ],
            "language": "en"
        }
        transcription_service.model_service.transcribe.return_value = mock_result
        
        # Mock tenant upload
        mock_tenant_client.return_value.upload_transcription.return_value = True
        
        # Execute transcription
        result = transcription_service.transcribe(
            parley_id=123456,
            customer_id=100069,
            language="en"
        )
        
        # Verify result
        assert result is not None
        assert result["language"] == "en"
        assert len(result["segments"]) == 1

    def test_transcribe_unsupported_language(self, transcription_service):
        """Test transcription with unsupported language."""
        transcription_service.model_service.transcribe.return_value = {
            "language": "zh",  # Unsupported language
            "segments": []
        }
        
        with pytest.raises(Exception) as exc_info:
            transcription_service.transcribe(123456, 100069, "en")
        
        assert "Unsupported language" in str(exc_info.value)


class TestLLMModelService:
    @pytest.fixture
    def mock_configuration(self):
        config = Mock(spec=ServiceConfiguration)
        config.llm_settings = {
            "model_name": "test-model",
            "max_length": 512,
            "temperature": 0.7
        }
        return config

    @patch('llm_model.services.torch')
    @patch('llm_model.services.AutoTokenizer')
    @patch('llm_model.services.AutoModelForCausalLM')
    def test_model_loading(self, mock_model, mock_tokenizer, mock_torch, mock_configuration):
        """Test LLM model loading."""
        mock_torch.cuda.is_available.return_value = True
        mock_tokenizer.from_pretrained.return_value = Mock()
        mock_model.from_pretrained.return_value = Mock()
        
        service = LLMModelService(mock_configuration)
        
        assert service.model is not None
        assert service.tokenizer is not None
        assert service.cuda_available is True

    def test_generate_text(self, mock_configuration):
        """Test text generation."""
        with patch('llm_model.services.torch'), \
             patch('llm_model.services.AutoTokenizer'), \
             patch('llm_model.services.AutoModelForCausalLM'):
            
            service = LLMModelService(mock_configuration)
            
            # Mock tokenizer and model
            service.tokenizer.encode.return_value = [1, 2, 3]
            service.tokenizer.decode.return_value = "Generated text"
            service.model.generate.return_value = [[1, 2, 3, 4, 5]]
            
            result = service.generate("Test prompt")
            
            assert result == "Generated text"
```

### API Tests

```python
# tests/test_api.py
import pytest
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from worker.models import WorkerHost, WorkflowJob
from llm_model.models import ApiInferenceAsyncJob


class TestHealthCheckAPI(TestCase):
    def setUp(self):
        self.client = APIClient()

    @patch('api.services.get_system_health')
    def test_health_check_success(self, mock_health):
        """Test successful health check."""
        mock_health.return_value = {
            "status": "ok",
            "database": "ok",
            "rabbitmq": "ok",
            "consumers": "ok"
        }
        
        url = reverse('health_check')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['status'], 'ok')

    def test_worker_health_endpoint(self):
        """Test worker health endpoint."""
        # Create test worker
        WorkerHost.objects.create(
            name="test-worker",
            hostname="test-host",
            ec2_type="t3.medium",
            status="running"
        )
        
        url = reverse('workerhost_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]['name'], 'test-worker')


class TestWorkflowJobAPI(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.workflow_job = WorkflowJob.objects.create(
            parley_id=123456,
            customer_id=100069,
            language="en",
            requested_services="transcription,summary",
            status="completed"
        )

    def test_list_workflow_jobs(self):
        """Test listing workflow jobs."""
        url = reverse('workflowjob-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['count'], 1)

    def test_filter_workflow_jobs_by_status(self):
        """Test filtering workflow jobs by status."""
        url = reverse('workflowjob-list')
        response = self.client.get(url, {'status': 'completed'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['count'], 1)

    def test_filter_workflow_jobs_by_customer(self):
        """Test filtering workflow jobs by customer."""
        url = reverse('workflowjob-list')
        response = self.client.get(url, {'customer_id': 100069})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['count'], 1)


class TestLLMInferenceAPI(TestCase):
    def setUp(self):
        self.client = APIClient()

    @patch('llm_model.tasks.api_task.delay')
    def test_create_inference_job(self, mock_task):
        """Test creating an inference job."""
        mock_task.return_value.id = "test-task-id"
        
        data = {
            "input_data": {
                "prompt": "Test prompt",
                "model_name": "test-model"
            },
            "generation_config": {
                "max_length": 512,
                "temperature": 0.7
            }
        }
        
        url = reverse('api_inference_jobs-list')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()['task_id'], 'test-task-id')
        self.assertEqual(response.json()['status'], 'pending')

    def test_get_inference_job(self):
        """Test retrieving an inference job."""
        job = ApiInferenceAsyncJob.objects.create(
            input_data={"prompt": "test"},
            status="completed",
            result={"generated_text": "test result"}
        )
        
        url = reverse('api_inference_jobs-detail', kwargs={'pk': job.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['status'], 'completed')
        self.assertEqual(response.json()['result']['generated_text'], 'test result')
```

## Integration Testing

### Celery Task Tests

```python
# tests/test_tasks.py
import pytest
from unittest.mock import patch, Mock
from celery.exceptions import Retry
from transcription.tasks import transcription_task
from llm_model.tasks import api_task
from worker.models import ServiceJob, WorkflowJob


class TestTranscriptionTask:
    @pytest.fixture
    def service_job(self):
        workflow_job = WorkflowJob.objects.create(
            parley_id=123456,
            customer_id=100069,
            language="en",
            status="started"
        )
        return ServiceJob.objects.create(
            workflow_job=workflow_job,
            service_type="transcription",
            parley_id=123456,
            customer_id=100069,
            language="en"
        )

    @patch('transcription.tasks.TranscriptionService')
    def test_transcription_task_success(self, mock_service, service_job):
        """Test successful transcription task."""
        # Mock service
        mock_service_instance = Mock()
        mock_service_instance.transcribe.return_value = {
            "segments": [{"start": 0, "end": 5, "text": "Hello"}],
            "language": "en"
        }
        mock_service.return_value = mock_service_instance
        
        # Execute task
        result = transcription_task.apply(kwargs={
            'service_job_id': service_job.id,
            'parley_id': 123456,
            'customer_id': 100069,
            'language': 'en'
        })
        
        # Verify result
        assert result.successful()
        service_job.refresh_from_db()
        assert service_job.status == "completed"

    @patch('transcription.tasks.TranscriptionService')
    def test_transcription_task_retry(self, mock_service, service_job):
        """Test transcription task retry on failure."""
        # Mock service to raise exception
        mock_service_instance = Mock()
        mock_service_instance.transcribe.side_effect = Exception("Temporary error")
        mock_service.return_value = mock_service_instance
        
        # Execute task
        with pytest.raises(Retry):
            transcription_task.apply(kwargs={
                'service_job_id': service_job.id,
                'parley_id': 123456,
                'customer_id': 100069,
                'language': 'en'
            })


class TestLLMTask:
    @pytest.fixture
    def inference_job(self):
        from llm_model.models import ApiInferenceAsyncJob
        return ApiInferenceAsyncJob.objects.create(
            input_data={"prompt": "Test prompt"},
            status="pending"
        )

    @patch('llm_model.tasks.ApiService')
    def test_api_task_success(self, mock_service, inference_job):
        """Test successful LLM API task."""
        # Mock service
        mock_service_instance = Mock()
        mock_service_instance.generate.return_value = "Generated response"
        mock_service.return_value = mock_service_instance
        
        # Execute task
        result = api_task.apply(kwargs={
            'async_job_id': inference_job.id
        })
        
        # Verify result
        assert result.successful()
        inference_job.refresh_from_db()
        assert inference_job.status == "completed"
        assert inference_job.result is not None
```

### Database Integration Tests

```python
# tests/test_database_integration.py
import pytest
from django.test import TransactionTestCase
from django.db import transaction
from worker.models import WorkflowJob, ServiceJob
from worker.services import ServiceOrchestrator


class TestDatabaseIntegration(TransactionTestCase):
    def test_workflow_creation_atomic(self):
        """Test that workflow creation is atomic."""
        orchestrator = ServiceOrchestrator()
        
        # Test successful workflow creation
        workflow_job = orchestrator.create_workflow(
            parley_id=123456,
            customer_id=100069,
            language="en",
            requested_services=["transcription", "summary"]
        )
        
        assert workflow_job is not None
        assert workflow_job.service_jobs.count() == 2
        
        # Verify all service jobs are created
        transcription_job = workflow_job.service_jobs.filter(service_type="transcription").first()
        summary_job = workflow_job.service_jobs.filter(service_type="summary").first()
        
        assert transcription_job is not None
        assert summary_job is not None

    def test_workflow_rollback_on_error(self):
        """Test that workflow creation rolls back on error."""
        orchestrator = ServiceOrchestrator()
        
        # Mock an error during service job creation
        with patch('worker.models.ServiceJob.objects.create') as mock_create:
            mock_create.side_effect = Exception("Database error")
            
            with pytest.raises(Exception):
                orchestrator.create_workflow(
                    parley_id=123456,
                    customer_id=100069,
                    language="en",
                    requested_services=["transcription"]
                )
            
            # Verify no workflow job was created
            assert WorkflowJob.objects.count() == 0
            assert ServiceJob.objects.count() == 0
```

## Performance Testing

### Load Testing with Locust

Create `tests/load_test.py`:

```python
from locust import HttpUser, task, between
import json
import random


class CODAUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """Setup for each user."""
        self.base_url = "/codaapi"
    
    @task(3)
    def health_check(self):
        """Test health check endpoint."""
        self.client.get(f"{self.base_url}/health/")
    
    @task(2)
    def list_workflow_jobs(self):
        """Test workflow jobs listing."""
        self.client.get(f"{self.base_url}/workflow-jobs/")
    
    @task(1)
    def create_inference_job(self):
        """Test LLM inference job creation."""
        data = {
            "input_data": {
                "prompt": f"Test prompt {random.randint(1, 1000)}",
                "model_name": "test-model"
            }
        }
        self.client.post(
            f"{self.base_url}/inference-jobs/",
            json=data,
            headers={"Content-Type": "application/json"}
        )
    
    @task(1)
    def worker_health(self):
        """Test worker health endpoint."""
        self.client.get(f"{self.base_url}/health/workers/")


class AdminUser(HttpUser):
    wait_time = between(2, 5)
    
    @task
    def admin_pages(self):
        """Test admin interface."""
        # Note: This would require authentication setup
        pass
```

Run load tests:

```bash
# Install locust
pip install locust

# Run load test
locust -f tests/load_test.py --host=http://localhost:8000

# Run headless load test
locust -f tests/load_test.py --host=http://localhost:8000 --users 50 --spawn-rate 5 --run-time 300s --headless
```

### GPU Performance Tests

```python
# tests/test_gpu_performance.py
import pytest
import time
from unittest.mock import patch
from transcription.services import TranscriptionModelService
from llm_model.services import LLMModelService


@pytest.mark.gpu
class TestGPUPerformance:
    def test_transcription_memory_usage(self):
        """Test transcription GPU memory usage."""
        with patch('transcription.services.whisperx') as mock_whisperx:
            service = TranscriptionModelService()
            
            # Monitor GPU memory before and after
            initial_memory = self.get_gpu_memory()
            
            # Process multiple files
            for i in range(5):
                service.process_channel(f"/tmp/test_{i}.wav")
            
            final_memory = self.get_gpu_memory()
            memory_increase = final_memory - initial_memory
            
            # Assert memory usage is reasonable
            assert memory_increase < 2000  # Less than 2GB increase
    
    def test_llm_inference_speed(self):
        """Test LLM inference speed."""
        with patch('llm_model.services.AutoModelForCausalLM'), \
             patch('llm_model.services.AutoTokenizer'):
            
            service = LLMModelService()
            
            # Test inference speed
            start_time = time.time()
            result = service.generate("Test prompt for speed testing")
            end_time = time.time()
            
            inference_time = end_time - start_time
            
            # Assert reasonable inference time
            assert inference_time < 10.0  # Less than 10 seconds
            assert result is not None
    
    @staticmethod
    def get_gpu_memory():
        """Get current GPU memory usage in MB."""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            return info.used / 1024 / 1024  # Convert to MB
        except:
            return 0
```

## End-to-End Testing

### Full Workflow Tests

```python
# tests/test_e2e.py
import pytest
from django.test import TransactionTestCase
from unittest.mock import patch, Mock
from worker.services import ServiceOrchestrator
from worker.models import WorkflowJob, ServiceJob


class TestEndToEndWorkflow(TransactionTestCase):
    @patch('transcription.tasks.transcription_task.delay')
    @patch('llm_model.tasks.api_task.delay')
    def test_complete_workflow(self, mock_llm_task, mock_transcription_task):
        """Test complete workflow from message to completion."""
        # Mock task results
        mock_transcription_task.return_value.id = "transcription-task-id"
        mock_llm_task.return_value.id = "llm-task-id"
        
        # Create workflow
        orchestrator = ServiceOrchestrator()
        workflow_job = orchestrator.delay_workflow(
            parley_id=123456,
            customer_id=100069,
            language="en",
            requested_services=["transcription", "summary"]
        )
        
        # Verify workflow creation
        assert workflow_job is not None
        assert workflow_job.status == "started"
        assert workflow_job.service_jobs.count() == 2
        
        # Verify tasks were scheduled
        mock_transcription_task.assert_called_once()
        mock_llm_task.assert_called_once()
        
        # Simulate task completion
        transcription_job = workflow_job.service_jobs.filter(service_type="transcription").first()
        transcription_job.mark_completed()
        
        summary_job = workflow_job.service_jobs.filter(service_type="summary").first()
        summary_job.mark_completed()
        
        # Check workflow completion
        workflow_job.refresh_from_db()
        if all(job.status == "completed" for job in workflow_job.service_jobs.all()):
            workflow_job.mark_completed()
        
        assert workflow_job.status == "completed"
```

## Test Data Management

### Factory Classes

```python
# tests/factories.py
import factory
from django.contrib.auth.models import User
from worker.models import WorkerHost, WorkflowJob, ServiceJob
from service.models import ServiceConfiguration


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
    
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")


class WorkerHostFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = WorkerHost
    
    name = factory.Sequence(lambda n: f"worker-{n:02d}")
    hostname = factory.Faker("hostname")
    ec2_type = factory.Iterator(["t3.medium", "g4dn.xlarge", "p3.2xlarge"])
    status = "running"


class WorkflowJobFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = WorkflowJob
    
    parley_id = factory.Faker("random_int", min=100000, max=999999)
    customer_id = factory.Faker("random_int", min=100000, max=200000)
    language = "en"
    requested_services = "transcription,summary"
    status = "pending"


class ServiceJobFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ServiceJob
    
    workflow_job = factory.SubFactory(WorkflowJobFactory)
    service_type = factory.Iterator(["transcription", "summary", "redflag"])
    parley_id = factory.LazyAttribute(lambda obj: obj.workflow_job.parley_id)
    customer_id = factory.LazyAttribute(lambda obj: obj.workflow_job.customer_id)
    language = factory.LazyAttribute(lambda obj: obj.workflow_job.language)
    status = "pending"
```

## Running Tests

### Local Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit
pytest -m integration
pytest -m "not slow"

# Run with coverage
pytest --cov=. --cov-report=html

# Run parallel tests
pytest -n auto

# Run specific test file
pytest tests/test_models.py

# Run specific test
pytest tests/test_models.py::TestWorkerHost::test_worker_host_creation
```

### CI/CD Testing

```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test
          MYSQL_DATABASE: test_coda
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      rabbitmq:
        image: rabbitmq:3.12
        env:
          RABBITMQ_DEFAULT_USER: test
          RABBITMQ_DEFAULT_PASS: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: |
        cd src
        pytest --cov=. --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./src/coverage.xml
```

## Test Best Practices

### 1. Test Organization
- Group related tests in classes
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

### 2. Mocking Strategy
- Mock external dependencies
- Use dependency injection for testability
- Mock at the boundary of your system

### 3. Test Data
- Use factories for test data generation
- Keep test data minimal and focused
- Clean up test data after tests

### 4. Performance
- Run fast tests frequently
- Mark slow tests appropriately
- Use parallel test execution

### 5. Coverage
- Aim for high test coverage (>80%)
- Focus on critical business logic
- Don't chase 100% coverage blindly
