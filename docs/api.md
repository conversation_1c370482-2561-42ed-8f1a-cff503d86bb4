# CODA API Documentation

## Overview

The CODA API provides RESTful endpoints for system monitoring, job management, and LLM inference. The API is built using Django REST Framework and includes comprehensive OpenAPI documentation.

## Base Information

- **Base URL**: `http://localhost:8000/codaapi/`
- **API Version**: Latest
- **Content Type**: `application/json`
- **Authentication**: Session-based (Django admin) or API key

## Interactive Documentation

- **Swagger UI**: `http://localhost:8000/codaapi/docs/`
- **ReDoc**: `http://localhost:8000/codaapi/redoc/`
- **OpenAPI Schema**: `http://localhost:8000/codaapi/schema/`

## Authentication

### Session Authentication
For web-based access, use Django's session authentication:
```bash
# Login via Django admin first, then use session cookies
curl -X GET "http://localhost:8000/codaapi/health/" \
  -H "Cookie: sessionid=your-session-id"
```

### API Key Authentication
For programmatic access, API keys can be configured (implementation depends on your setup).

## Health and Monitoring Endpoints

### System Health Check

**Endpoint**: `GET /health/`

**Description**: Comprehensive system health check including database, RabbitMQ, and worker status.

**Response**:
```json
{
  "status": "ok",
  "database": "ok",
  "rabbitmq": "ok",
  "consumers": "ok",
  "timestamp": "2024-06-18T10:30:00Z"
}
```

**Status Values**:
- `ok` - Service is healthy
- `error` - Service has issues
- `unavailable` - Service is not accessible

**Example**:
```bash
curl -X GET "http://localhost:8000/codaapi/health/"
```

### Worker Health Check

**Endpoint**: `GET /health/workers/`

**Description**: Detailed status of all worker hosts in the system.

**Response**:
```json
[
  {
    "id": 1,
    "name": "transcription-worker-01",
    "hostname": "gpu-node-01",
    "ec2_type": "g4dn.xlarge",
    "status": "running",
    "healthy": true,
    "down_for_maintenance": false,
    "ping_response": "ok",
    "last_seen": "2024-06-18T10:29:45Z"
  }
]
```

**Worker Status Values**:
- `running` - Worker is active and processing tasks
- `stopped` - Worker is not running
- `loading` - Worker is starting up or loading models

**Example**:
```bash
curl -X GET "http://localhost:8000/codaapi/health/workers/"
```

## Job Management Endpoints

### Workflow Jobs

**Endpoint**: `GET /workflow-jobs/`

**Description**: List and filter workflow jobs (high-level job tracking).

**Query Parameters**:
- `status` - Filter by job status (`pending`, `started`, `completed`, `failed`)
- `customer_id` - Filter by customer ID
- `parley_id` - Filter by parley ID
- `language` - Filter by language code
- `ordering` - Sort results (`-created_at`, `started_at`, etc.)
- `page` - Page number for pagination
- `page_size` - Number of results per page (default: 20)

**Response**:
```json
{
  "count": 150,
  "next": "http://localhost:8000/codaapi/workflow-jobs/?page=2",
  "previous": null,
  "results": [
    {
      "id": 123,
      "parley_id": 456789,
      "customer_id": 100069,
      "language": "en",
      "requested_services": "transcription,summary",
      "status": "completed",
      "started_at": "2024-06-18T10:00:00Z",
      "completed_at": "2024-06-18T10:05:30Z",
      "duration": "00:05:30",
      "service_jobs": [
        {
          "id": 234,
          "service_type": "transcription",
          "status": "completed"
        }
      ]
    }
  ]
}
```

**Example**:
```bash
# Get all completed workflow jobs
curl -X GET "http://localhost:8000/codaapi/workflow-jobs/?status=completed"

# Get jobs for specific customer
curl -X GET "http://localhost:8000/codaapi/workflow-jobs/?customer_id=100069"
```

### Service Jobs

**Endpoint**: `GET /service-jobs/`

**Description**: List and filter individual service jobs (detailed task tracking).

**Query Parameters**:
- `status` - Filter by job status
- `service_type` - Filter by service type (`transcription`, `summary`, `redflag`)
- `workflow_job` - Filter by parent workflow job ID
- `customer_id` - Filter by customer ID
- `parley_id` - Filter by parley ID

**Response**:
```json
{
  "count": 300,
  "next": "http://localhost:8000/codaapi/service-jobs/?page=2",
  "previous": null,
  "results": [
    {
      "id": 234,
      "workflow_job": 123,
      "service_type": "transcription",
      "parley_id": 456789,
      "customer_id": 100069,
      "language": "en",
      "status": "completed",
      "started_at": "2024-06-18T10:00:15Z",
      "completed_at": "2024-06-18T10:03:45Z",
      "duration": "00:03:30",
      "transcription_retrieved_from_s3": true,
      "error": null
    }
  ]
}
```

**Example**:
```bash
# Get all transcription jobs
curl -X GET "http://localhost:8000/codaapi/service-jobs/?service_type=transcription"

# Get failed jobs
curl -X GET "http://localhost:8000/codaapi/service-jobs/?status=failed"
```

## LLM Inference Endpoints

### Async Inference Jobs

**Endpoint**: `POST /inference-jobs/`

**Description**: Create a new asynchronous LLM inference job.

**Request Body**:
```json
{
  "input_data": {
    "prompt": "Summarize the following conversation: [conversation text]",
    "model_name": "summary_model",
    "context": "Customer service call"
  },
  "generation_config": {
    "max_length": 512,
    "temperature": 0.7,
    "top_p": 0.9
  }
}
```

**Response**:
```json
{
  "id": 456,
  "task_id": "celery-task-uuid-here",
  "status": "pending",
  "input_data": {
    "prompt": "Summarize the following conversation: [conversation text]",
    "model_name": "summary_model",
    "context": "Customer service call"
  },
  "generation_config": {
    "max_length": 512,
    "temperature": 0.7,
    "top_p": 0.9
  },
  "result": null,
  "created_at": "2024-06-18T10:30:00Z",
  "started_at": null,
  "completed_at": null
}
```

**Example**:
```bash
curl -X POST "http://localhost:8000/codaapi/inference-jobs/" \
  -H "Content-Type: application/json" \
  -d '{
    "input_data": {
      "prompt": "Analyze this text for sentiment",
      "model_name": "redflag_model"
    }
  }'
```

### Get Inference Job Status

**Endpoint**: `GET /inference-jobs/{id}/`

**Description**: Get the status and results of a specific inference job.

**Response**:
```json
{
  "id": 456,
  "task_id": "celery-task-uuid-here",
  "status": "completed",
  "input_data": {
    "prompt": "Analyze this text for sentiment",
    "model_name": "redflag_model"
  },
  "generation_config": {
    "max_length": 512,
    "temperature": 0.7
  },
  "result": {
    "generated_text": "The sentiment of this text is positive...",
    "confidence": 0.85,
    "processing_time": 2.3
  },
  "created_at": "2024-06-18T10:30:00Z",
  "started_at": "2024-06-18T10:30:05Z",
  "completed_at": "2024-06-18T10:30:07Z"
}
```

**Example**:
```bash
curl -X GET "http://localhost:8000/codaapi/inference-jobs/456/"
```

### List Inference Jobs

**Endpoint**: `GET /inference-jobs/`

**Description**: List all inference jobs with filtering and pagination.

**Query Parameters**:
- `status` - Filter by status (`pending`, `started`, `completed`, `failed`)
- `ordering` - Sort results (`-created_at`, `completed_at`, etc.)

**Example**:
```bash
# Get all completed inference jobs
curl -X GET "http://localhost:8000/codaapi/inference-jobs/?status=completed"
```

## Error Handling

### HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Permission denied
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

### Error Response Format

```json
{
  "error": "Invalid request",
  "detail": "The 'input_data' field is required.",
  "code": "validation_error"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default Limit**: 100 requests per minute per IP
- **Burst Limit**: 200 requests per minute
- **Headers**: Rate limit information is included in response headers

## Pagination

List endpoints use cursor-based pagination:

```json
{
  "count": 1000,
  "next": "http://localhost:8000/codaapi/workflow-jobs/?page=2",
  "previous": null,
  "results": [...]
}
```

**Query Parameters**:
- `page` - Page number
- `page_size` - Results per page (max 100, default 20)

## Filtering and Ordering

### Common Filters

Most list endpoints support:
- `status` - Filter by status
- `created_at__gte` - Created after date
- `created_at__lte` - Created before date
- `customer_id` - Filter by customer
- `search` - Text search (where applicable)

### Ordering

Use the `ordering` parameter:
- `ordering=created_at` - Ascending order
- `ordering=-created_at` - Descending order
- `ordering=status,-created_at` - Multiple fields

**Example**:
```bash
curl -X GET "http://localhost:8000/codaapi/workflow-jobs/?status=completed&ordering=-created_at"
```

## SDK and Client Libraries

### Python Client Example

```python
import requests

class CODAClient:
    def __init__(self, base_url, session_id=None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        if session_id:
            self.session.cookies.set('sessionid', session_id)
    
    def health_check(self):
        response = self.session.get(f"{self.base_url}/health/")
        return response.json()
    
    def create_inference_job(self, input_data, generation_config=None):
        data = {"input_data": input_data}
        if generation_config:
            data["generation_config"] = generation_config
        
        response = self.session.post(
            f"{self.base_url}/inference-jobs/",
            json=data
        )
        return response.json()
    
    def get_inference_job(self, job_id):
        response = self.session.get(f"{self.base_url}/inference-jobs/{job_id}/")
        return response.json()

# Usage
client = CODAClient("http://localhost:8000/codaapi")
health = client.health_check()
print(f"System status: {health['status']}")
```

## Webhooks (Future Enhancement)

Planned webhook support for job completion notifications:

```json
{
  "event": "job.completed",
  "job_id": 123,
  "job_type": "workflow",
  "status": "completed",
  "timestamp": "2024-06-18T10:30:00Z",
  "data": {
    "parley_id": 456789,
    "customer_id": 100069,
    "services": ["transcription", "summary"]
  }
}
```
