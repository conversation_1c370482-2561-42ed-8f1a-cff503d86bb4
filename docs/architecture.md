# CODA System Architecture

## Overview

CODA (Conversational Operations and Data Analytics) is a distributed microservices system designed for high-performance audio processing and natural language analysis. The architecture follows a message-driven, event-based pattern with horizontal scaling capabilities.

## System Architecture Principles

### 1. Microservices Design
- **Separation of Concerns**: Each service handles a specific domain (transcription, LLM processing, workflow management)
- **Independent Deployment**: Services can be deployed and scaled independently
- **Technology Diversity**: Different services can use optimal technologies for their specific needs

### 2. Message-Driven Architecture
- **Asynchronous Processing**: All heavy processing is handled asynchronously via message queues
- **Loose Coupling**: Services communicate through well-defined message contracts
- **Fault Tolerance**: Message persistence and retry mechanisms ensure reliability

### 3. Horizontal Scalability
- **Worker Pools**: Multiple instances of each worker type can run simultaneously
- **Queue-Based Load Distribution**: Work is distributed automatically across available workers
- **GPU Resource Management**: Efficient utilization of expensive GPU resources

## Core Components

### 1. Django Web Application (Admin/API)

**Purpose**: Central management and API interface

**Components**:
- Django Admin interface for system configuration
- REST API endpoints for external integration
- Health monitoring and system status
- User authentication and authorization

**Key Files**:
- `src/coda/settings/` - Configuration management
- `src/api/` - REST API implementation
- `src/coda/urls.py` - URL routing

### 2. Messenger System

**Purpose**: Message ingestion and workflow orchestration

**Functionality**:
- Consumes messages from RabbitMQ messenger queue
- Validates incoming service requests
- Creates workflow jobs and distributes tasks
- Implements rate limiting and error handling

**Key Components**:
- `MessengerConfiguration` - Runtime configuration
- `ServiceOrchestrator` - Task creation and distribution
- Rate limiting with configurable thresholds
- Message validation and error handling

**Message Flow**:
```
External System → RabbitMQ → Messenger → ServiceOrchestrator → Task Queues
```

### 3. Worker System

#### 3.1 Transcription Workers (GPU)

**Purpose**: Audio-to-text conversion using GPU acceleration

**Technology Stack**:
- WhisperX 3.3 for speech recognition
- PyTorch with CUDA support
- FFmpeg for audio processing

**Process Flow**:
1. Receive transcription task from queue
2. Download audio file from S3
3. Process audio through WhisperX model
4. Upload transcription results to tenant database
5. Update job status and metrics

**Configuration**:
- Model selection (whisper-large-v3, etc.)
- Batch size optimization
- Language detection and filtering
- GPU memory management

#### 3.2 LLM Workers (GPU)

**Purpose**: Natural language processing and content analysis

**Capabilities**:
- Content summarization
- Red flag detection
- Custom prompt processing
- Multi-model support (local and remote APIs)

**Supported Models**:
- Local transformer models via Hugging Face
- Remote API models (OpenAI-compatible)
- Custom fine-tuned models

**Process Flow**:
1. Receive LLM task from queue (summary/redflag)
2. Load appropriate model and configuration
3. Format input using prompt templates
4. Generate response using model
5. Post-process and store results

#### 3.3 CPU Workers

**Purpose**: General-purpose task processing

**Responsibilities**:
- Database operations
- File management tasks
- Integration with external systems
- Non-GPU intensive processing

### 4. Queue System (Celery + RabbitMQ)

**Queue Structure**:
- `messenger` - Incoming message processing
- `transcription` - Audio transcription tasks
- `summary` - Content summarization tasks
- `redflag` - Content analysis tasks
- `default` - General CPU tasks

**Configuration**:
- Priority-based task routing
- Dead letter queue handling
- Task retry mechanisms
- Worker concurrency limits

## Data Architecture

### 1. Database Design

#### Primary Database (CODA)
- **Purpose**: System configuration and job tracking
- **Tables**:
  - `WorkflowJob` - High-level workflow tracking
  - `ServiceJob` - Individual service task tracking
  - `WorkerHost` - Worker node management
  - `ServiceConfiguration` - Service settings
  - `MessengerConfiguration` - Messenger settings

#### Tenant Databases
- **Purpose**: Customer-specific data storage
- **Tables**:
  - Transcription results
  - Customer configurations
  - Audio file metadata

#### Estate Database
- **Purpose**: Customer and tenant management
- **Tables**:
  - Customer information
  - Tenant configurations
  - Service entitlements

### 2. Data Flow

```
Audio Files (S3) → Transcription Workers → Tenant DB
                ↓
            LLM Workers → Analysis Results → Tenant DB
                ↓
            Workflow Tracking → CODA DB
```

## Service Configuration

### 1. Service Configuration Model

Each service (transcription, summary, redflag) has configurable parameters:

**Transcription Configuration**:
- Model selection and parameters
- Batch size and memory settings
- Language support
- Audio processing options

**LLM Configuration**:
- Model selection (local/remote)
- Generation parameters
- Prompt templates
- Context length limits

### 2. Dynamic Configuration

- Configuration changes take effect without service restart
- Model reloading when configuration changes
- Hot-swappable prompt templates

## Monitoring and Observability

### 1. Health Checks

**System Health Endpoint**: `/codaapi/health/`
- Database connectivity
- RabbitMQ connectivity
- Worker status
- Queue health

**Worker Health Monitoring**:
- Individual worker status
- GPU memory usage
- Task processing metrics
- Error rates

### 2. Metrics and Logging

**Structured Logging**:
- JSON-formatted logs
- Correlation IDs for request tracking
- Performance metrics
- Error tracking

**Key Metrics**:
- Task processing times
- Queue depths
- Worker utilization
- Error rates
- GPU memory usage

## Security Architecture

### 1. Authentication and Authorization

- Django's built-in authentication system
- API key-based authentication for external systems
- Role-based access control

### 2. Data Security

- Encrypted data transmission
- Secure credential management
- Customer data isolation
- Audit logging

## Deployment Architecture

### 1. Container Strategy

**Multi-stage Docker builds**:
- `base` - Common dependencies
- `app` - Web application and CPU workers
- `transcription` - GPU workers with WhisperX
- `llm_generic` - GPU workers with transformer models

### 2. Scaling Strategy

**Horizontal Scaling**:
- Multiple instances of each worker type
- Load balancing across worker pools
- Auto-scaling based on queue depth

**Resource Management**:
- GPU resource allocation
- Memory optimization
- CPU affinity settings

## Integration Patterns

### 1. External System Integration

**Message-based Integration**:
- RabbitMQ message consumption
- Webhook callbacks for completion notifications
- REST API for status queries

**File Storage Integration**:
- S3-compatible storage for audio files
- Secure file access with temporary URLs
- Multi-region support

### 2. Database Integration

**Multi-database Support**:
- Primary CODA database for system data
- Tenant-specific databases for customer data
- Read/write splitting capabilities

## Performance Considerations

### 1. GPU Optimization

- Model loading optimization
- Memory management
- Batch processing
- Model caching strategies

### 2. Queue Optimization

- Priority-based task routing
- Worker prefetch limits
- Task batching where applicable
- Dead letter queue handling

### 3. Database Optimization

- Connection pooling
- Query optimization
- Index strategies
- Partitioning for large datasets

## Disaster Recovery

### 1. Backup Strategy

- Database backups
- Configuration backups
- Model and checkpoint backups

### 2. Failover Mechanisms

- Multi-region deployment support
- Database failover
- Queue persistence
- Graceful degradation

## Future Architecture Considerations

### 1. Planned Enhancements

- Kubernetes deployment support
- Advanced auto-scaling
- Multi-cloud support
- Enhanced monitoring and alerting

### 2. Scalability Roadmap

- Microservice decomposition
- Event sourcing patterns
- CQRS implementation
- Stream processing capabilities
