# CODA Admin Interface User Guide

## Overview

The CODA Admin Interface is a web-based Django administration panel that provides comprehensive management capabilities for the CODA system. It allows administrators to manage services, workers, configurations, and monitor system operations through an intuitive web interface.

## Accessing the Admin Interface

### Prerequisites
- CODA system must be running
- Admin user account with appropriate permissions
- Web browser access to the CODA server

### Access URLs
- **Development**: `http://localhost:8000/admin/`
- **Production**: `https://your-domain.com/admin/`

### Login
1. Navigate to the admin URL
2. Enter your username and password
3. Click "Log in"

## Main Admin Sections

### 1. Service Management

#### Service Configurations
**Location**: Admin → Service → Service configurations

**Purpose**: Manage service configurations for transcription, summarization, and red-flag analysis.

**Key Features**:
- Create and edit service configurations
- JSON-based configuration editor with syntax highlighting
- Duplicate configurations for easy templating
- Search configurations by name

**Common Tasks**:
- **Create New Configuration**: Click "Add service configuration" → Fill in name and settings → Save
- **Edit Configuration**: Click on configuration name → Modify JSON settings → Save
- **Duplicate Configuration**: Select configuration → Choose "Duplicate instance" action → Execute

#### CODA Services
**Location**: Admin → Service → CODA services

**Purpose**: Manage available services (transcription, summarization, red-flag analysis).

**Key Features**:
- Enable/disable services
- Link services to configurations
- View service descriptions

### 2. Worker Management

#### Worker Hosts
**Location**: Admin → Worker → Worker hosts

**Purpose**: Monitor and manage worker nodes in the CODA cluster.

**Key Features**:
- View worker status and health
- Monitor current concurrency/pool size
- Restart worker pools
- Grow/shrink worker pools
- Set maintenance mode

**Worker Actions**:
- **Pool Restart**: Select workers → Choose "Worker pool restart" → Execute
- **Grow Pool**: Select worker → Choose "Grow pool" → Specify process count → Execute
- **Shrink Pool**: Select worker → Choose "Shrink pool" → Specify process count → Execute

#### Workflow Jobs
**Location**: Admin → Worker → Workflow jobs

**Purpose**: Monitor workflow execution and job status.

**Key Features**:
- View job status (pending, started, completed, failed)
- Filter by customer ID, parley ID, language, status
- Monitor job duration and performance
- View associated service jobs

#### Service Jobs
**Location**: Admin → Worker → Service jobs

**Purpose**: Monitor individual service job execution.

**Key Features**:
- View detailed job information
- Filter by type, status, workflow, worker
- Monitor job duration and errors
- Link to associated workflows and workers

### 3. LLM Model Management

#### LLM Models
**Location**: Admin → Llm_model → LLM models

**Purpose**: Manage Large Language Models used for summarization and analysis.

**Key Features**:
- Configure model parameters
- Set generation configurations
- Manage tokenizer settings
- Duplicate model configurations

#### LLM Prompt Templates
**Location**: Admin → Llm_model → LLM prompt templates

**Purpose**: Manage prompt templates for different LLM tasks.

**Key Features**:
- Create and edit prompt templates
- JSON-based template configuration
- Template versioning and management

#### API Inference Jobs
**Location**: Admin → Llm_model → API inference async jobs

**Purpose**: Monitor and manage asynchronous LLM inference jobs.

**Key Features**:
- View job status and results
- Start/restart jobs manually
- Monitor job execution time
- View input data and generation configs

### 4. User Management

#### User Profiles
**Location**: Admin → Service → User profiles

**Purpose**: Manage user-specific settings and tenant configurations.

**Key Features**:
- Configure tenant ID usage
- Manage user-specific settings

## Advanced Features

### Custom Admin Views

#### System Information Page
**Location**: Admin → Info Page (custom link)

**Purpose**: View system version and status information.

**Features**:
- Current application version
- System timestamp
- Configuration details

### Bulk Operations

#### Bulk Actions
Most admin sections support bulk operations:
- Select multiple items using checkboxes
- Choose action from dropdown
- Click "Go" to execute

**Common Bulk Actions**:
- Delete selected items
- Duplicate configurations
- Start/stop services
- Restart worker pools

### Filtering and Search

#### Advanced Filtering
- Use filter sidebar to narrow results
- Filter by status, dates, IDs, etc.
- Combine multiple filters

#### Search Functionality
- Use search box for text-based searches
- Search across multiple fields
- Case-insensitive matching

## Monitoring and Troubleshooting

### Health Monitoring

#### Worker Health
- Check worker status in Worker Hosts section
- Monitor pool sizes and concurrency
- Look for offline or error states

#### Job Monitoring
- Monitor workflow and service job status
- Check for failed jobs and error messages
- Review job duration for performance issues

### Common Issues

#### Worker Issues
- **Worker Offline**: Check worker host status, restart if needed
- **High Load**: Consider growing worker pools
- **Stuck Jobs**: Check for failed dependencies or resource constraints

#### Configuration Issues
- **Invalid JSON**: Use JSON editor validation
- **Missing Dependencies**: Check service configuration requirements
- **Permission Errors**: Verify user permissions and access rights

## Best Practices

### Configuration Management
1. Always test configurations in development first
2. Use descriptive names for configurations
3. Document configuration changes
4. Keep backup copies of working configurations

### Worker Management
1. Monitor worker health regularly
2. Scale workers based on load patterns
3. Use maintenance mode for planned updates
4. Monitor resource usage and performance

### Security
1. Use strong passwords for admin accounts
2. Limit admin access to authorized personnel
3. Regularly review user permissions
4. Monitor admin activity logs

## Troubleshooting Guide

### Common Problems

#### Cannot Access Admin Interface
- Check if CODA admin service is running
- Verify network connectivity
- Check firewall settings
- Confirm correct URL and port

#### Login Issues
- Verify username and password
- Check user account status
- Confirm admin permissions
- Review authentication logs

#### Performance Issues
- Monitor database connections
- Check worker pool sizes
- Review system resource usage
- Optimize database queries

### Getting Help
- Check system logs for error messages
- Review worker status and health
- Monitor job queues for bottlenecks
- Contact system administrators for persistent issues

## Related Documentation
- [CLI Interface Guide](cli_interface_guide.md)
- [API Interface Guide](api_interface_guide.md)
- [Installation Guide](installation.md)
- [Developer Guide](dev_guide.md)
