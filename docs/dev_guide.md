# CODA Developer Guide

## Overview

This guide provides comprehensive information for developers working on the CODA system, including setup, development workflows, coding standards, and contribution guidelines.

## Development Environment Setup

### Prerequisites

- **Python 3.12+**: Required for Django and dependencies
- **Git**: Version control
- **Docker & Docker Compose**: For containerized development
- **Node.js 18+**: For frontend tooling (if applicable)
- **NVIDIA GPU**: For GPU worker development (optional)

### Quick Setup

1. **Clone the Repository**
   ```bash
   <NAME_EMAIL>:alex/coda.git
   cd coda
   ```

2. **Set Up Python Environment**
   ```bash
   # Using pyenv (recommended)
   pyenv install 3.12.4
   pyenv local 3.12.4

   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate

   # Install dependencies
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

3. **Environment Configuration**
   ```bash
   # Copy environment template
   cp .env.example .env.development

   # Edit configuration
   nano .env.development
   ```

4. **Database Setup**
   ```bash
   cd src
   python manage.py migrate
   python manage.py createsuperuser
   ```

5. **Start Development Server**
   ```bash
   python manage.py runserver
   ```

### Advanced Development Setup

#### Using direnv (Recommended)

**Install and configure `direnv` to automatically load environment variables:**

1. Install `direnv`:
   ```bash
   # Ubuntu/Debian
   sudo apt update && sudo apt install direnv

   # macOS
   brew install direnv

   # Other systems: https://direnv.net/docs/installation.html
   ```

2. Configure `direnv`:
   ```bash
   mkdir -p $HOME/.config/direnv
   echo -e "[global]\nload_dotenv = true" > $HOME/.config/direnv/direnv.toml
   ```

3. Add shell integration:
   ```bash
   # For bash
   echo 'eval "$(direnv hook bash)"' >> $HOME/.bashrc

   # For zsh
   echo 'eval "$(direnv hook zsh)"' >> $HOME/.zshrc

   # Apply changes
   source $HOME/.bashrc  # or ~/.zshrc
   ```

4. Create `.envrc` file in project root:
   ```bash
   # .envrc
   dotenv .env.development
   layout python python3.12
   ```

5. Allow direnv in the project:
   ```bash
   direnv allow
   ```

#### Docker Development Environment

For a fully containerized development environment:

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Access Django shell
docker-compose -f docker-compose.dev.yml exec web python manage.py shell

# Run tests
docker-compose -f docker-compose.dev.yml exec web pytest
```

## Project Structure

### High-Level Organization

```
coda/
├── docs/                   # Documentation
├── scripts/               # Deployment and utility scripts
├── src/                   # Main application code
│   ├── api/              # REST API endpoints
│   ├── coda/             # Django project settings
│   ├── common/           # Shared utilities
│   ├── estate/           # Customer/tenant management
│   ├── llm_model/        # LLM processing services
│   ├── service/          # Service configuration
│   ├── transcription/    # Audio transcription services
│   ├── worker/           # Worker management and tasks
│   └── manage.py         # Django management script
├── requirements*.txt      # Python dependencies
├── docker-compose*.yml   # Docker configurations
└── Dockerfile            # Container definitions
```

### Django Apps Overview

#### Core Apps

- **`coda/`**: Main Django project configuration, settings, and core utilities
- **`api/`**: REST API endpoints, serializers, and API-specific logic
- **`worker/`**: Worker management, job tracking, and task orchestration
- **`common/`**: Shared utilities, exceptions, and helper functions

#### Service Apps

- **`transcription/`**: Audio-to-text conversion using WhisperX
- **`llm_model/`**: LLM processing for summarization and analysis
- **`service/`**: Service configuration and management
- **`estate/`**: Customer and tenant data models

#### External Integration Apps

- **`tenant/`**: Tenant-specific database operations
- **`common/`**: External service clients (S3, tenant databases)

## Development Workflow

### Git Workflow

We follow a feature branch workflow:

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write code following our coding standards
   - Add tests for new functionality
   - Update documentation as needed

3. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

4. **Push and Create Merge Request**
   ```bash
   git push origin feature/your-feature-name
   # Create merge request in GitLab
   ```

### Commit Message Convention

We use conventional commits:

- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test additions or modifications
- `chore:` Maintenance tasks

Examples:
```
feat: add GPU memory monitoring to worker health checks
fix: resolve transcription task retry logic
docs: update API documentation for inference endpoints
test: add unit tests for LLM service configuration
```

### Code Review Process

1. **Self Review**: Review your own code before submitting
2. **Automated Checks**: Ensure CI/CD pipeline passes
3. **Peer Review**: At least one team member must review
4. **Testing**: Verify functionality in development environment
5. **Merge**: Squash and merge after approval

## Coding Standards

### Python Code Style

We follow PEP 8 with some modifications:

- **Line Length**: 120 characters (configured in `pyproject.toml`)
- **Imports**: Use absolute imports, group by standard/third-party/local
- **Docstrings**: Use Google-style docstrings
- **Type Hints**: Use type hints for function signatures

#### Code Formatting Tools

```bash
# Install formatting tools
pip install black isort flake8 mypy

# Format code
black src/
isort src/

# Check style
flake8 src/
mypy src/
```

#### Pre-commit Hooks

Set up pre-commit hooks for automatic formatting:

```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run manually
pre-commit run --all-files
```

### Django Best Practices

#### Models
- Use descriptive model names
- Add `__str__` methods for admin interface
- Use model managers for complex queries
- Add database indexes for frequently queried fields

```python
class WorkerHost(BaseModel):
    """Represents a worker host in the system."""

    name = models.CharField(max_length=130, unique=True)
    hostname = models.CharField(max_length=64)
    status = models.CharField(
        max_length=20,
        choices=WorkerHostStatusChoices.choices,
        default=WorkerHostStatusChoices.STOPPED
    )

    class Meta:
        verbose_name = "Worker Host"
        verbose_name_plural = "Worker Hosts"
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['hostname']),
        ]

    def __str__(self):
        return f"{self.name} ({self.hostname})"
```

#### Views and APIs
- Use class-based views for consistency
- Implement proper error handling
- Add comprehensive docstrings
- Use serializers for data validation

```python
class WorkerHostListAPIView(PublicViewMixin, ListAPIView):
    """
    API endpoint that returns a read-only list of WorkerHost objects.

    For health monitoring purposes, always check the 'healthy' attribute.
    If 'healthy' is False, examine 'status' and 'ping_response' attributes.
    """

    queryset = WorkerHost.objects.all()
    serializer_class = WorkerHostSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'healthy']
    ordering_fields = ['name', 'status', 'last_seen']
    ordering = ['name']
```

#### Services and Business Logic
- Keep business logic in service classes
- Use dependency injection for testability
- Handle exceptions gracefully
- Log important operations

```python
class TranscriptionService:
    """Service for handling audio transcription workflows."""

    def __init__(self, configuration: ServiceConfiguration):
        self.configuration = configuration
        self.model_service = TranscriptionModelService(configuration)
        self.logger = logging.getLogger(__name__)

    def transcribe(self, parley_id: int, customer_id: int, language: str) -> dict:
        """
        Transcribe audio for the given parley.

        Args:
            parley_id: Unique identifier for the conversation
            customer_id: Customer identifier
            language: Expected language code

        Returns:
            Transcription result dictionary

        Raises:
            TranscriptionServiceException: If transcription fails
        """
        try:
            self.logger.info(f"Starting transcription for parley {parley_id}")
            # Implementation here
        except Exception as e:
            self.logger.error(f"Transcription failed for parley {parley_id}: {e}")
            raise TranscriptionServiceException(f"Transcription failed: {e}")
```

## Testing Guidelines

### Test Structure

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test component interactions
- **API Tests**: Test REST endpoints
- **End-to-End Tests**: Test complete workflows

### Writing Tests

```python
class TestTranscriptionService:
    """Test suite for TranscriptionService."""

    @pytest.fixture
    def mock_configuration(self):
        """Create a mock service configuration."""
        config = Mock(spec=ServiceConfiguration)
        config.transcription_settings = Mock()
        return config

    @pytest.fixture
    def transcription_service(self, mock_configuration):
        """Create a TranscriptionService instance for testing."""
        with patch('transcription.services.TranscriptionModelService'):
            return TranscriptionService(mock_configuration)

    def test_transcribe_success(self, transcription_service):
        """Test successful transcription workflow."""
        # Arrange
        expected_result = {"segments": [], "language": "en"}
        transcription_service.model_service.transcribe.return_value = expected_result

        # Act
        result = transcription_service.transcribe(123456, 100069, "en")

        # Assert
        assert result == expected_result
        transcription_service.model_service.transcribe.assert_called_once()
```

### Running Tests

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit
pytest -m integration

# Run with coverage
pytest --cov=. --cov-report=html

# Run in parallel
pytest -n auto
```

## Debugging

### Django Debug Toolbar

For development, enable Django Debug Toolbar:

```python
# settings/development.py
if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
    INTERNAL_IPS = ['127.0.0.1']
```

### Logging Configuration

Use structured logging for better debugging:

```python
import logging

logger = logging.getLogger(__name__)

def process_transcription(parley_id: int):
    logger.info(
        "Processing transcription",
        extra={
            "parley_id": parley_id,
            "operation": "transcription_start"
        }
    )
```

### GPU Debugging

For GPU-related issues:

```bash
# Check GPU status
nvidia-smi

# Monitor GPU usage
watch -n 1 nvidia-smi

# Check CUDA availability in Python
python -c "import torch; print(torch.cuda.is_available())"
```

## Performance Optimization

### Database Optimization

- Use `select_related()` and `prefetch_related()` for foreign keys
- Add database indexes for frequently queried fields
- Use `bulk_create()` and `bulk_update()` for batch operations
- Monitor slow queries with Django Debug Toolbar

### Celery Task Optimization

- Keep tasks idempotent
- Use appropriate retry strategies
- Monitor task execution times
- Implement proper error handling

### GPU Memory Management

- Clear GPU cache between tasks
- Monitor VRAM usage
- Use appropriate batch sizes
- Implement model caching strategies

## Deployment

### Local Development

```bash
# Start development server
python manage.py runserver

# Start Celery worker
celery -A coda.celery worker -l DEBUG

# Start Celery flower
celery -A coda.celery flower
```

### Docker Development

```bash
# Build and start services
docker-compose -f docker-compose.dev.yml up --build

# View logs
docker-compose -f docker-compose.dev.yml logs -f web

# Execute commands in container
docker-compose -f docker-compose.dev.yml exec web python manage.py shell
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Check Python path and virtual environment
2. **Database Errors**: Verify database connection and migrations
3. **GPU Errors**: Check NVIDIA drivers and CUDA installation
4. **Task Failures**: Check Celery worker logs and RabbitMQ status

### Debug Commands

```bash
# Check Django configuration
python manage.py check

# Show migrations status
python manage.py showmigrations

# Test database connection
python manage.py dbshell

# Check Celery status
celery -A coda.celery inspect active
```

## Contributing

### Before Contributing

1. Read this developer guide thoroughly
2. Set up your development environment
3. Run the test suite to ensure everything works
4. Familiarize yourself with the codebase structure

### Contribution Process

1. **Issue Discussion**: Discuss significant changes in issues first
2. **Feature Branch**: Create a feature branch for your changes
3. **Code Quality**: Ensure code follows our standards
4. **Testing**: Add tests for new functionality
5. **Documentation**: Update documentation as needed
6. **Review**: Submit merge request for review

### Getting Help

- **Documentation**: Check existing documentation first
- **Code Comments**: Look for inline code comments
- **Team Chat**: Ask questions in team communication channels
- **Issues**: Create issues for bugs or feature requests

[Home](../README.md)

---
