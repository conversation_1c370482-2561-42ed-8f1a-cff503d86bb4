# CODA Deployment Guide

## Overview

This guide covers production deployment strategies for CODA, including Docker-based deployments, infrastructure requirements, and operational considerations.

## Deployment Architecture

### Recommended Production Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Servers   │    │   GPU Workers   │
│    (NGINX)      │────│   (Django)      │    │ (Transcription) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   CPU Workers   │    │   LLM Workers   │
                       │   (Celery)      │    │   (GPU-based)   │
                       └─────────────────┘    └─────────────────┘
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │    RabbitMQ     │    │     MySQL       │
                       │   (Message      │    │   (Database)    │
                       │    Broker)      │    │                 │
                       └─────────────────┘    └─────────────────┘
```

## Infrastructure Requirements

### Minimum System Requirements

#### Web/API Servers
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **Network**: 1Gbps
- **OS**: Ubuntu 22.04 LTS or RHEL 8+

#### GPU Workers (Transcription)
- **GPU**: NVIDIA T4, V100, A10, or A100
- **CPU**: 8 cores
- **RAM**: 32GB
- **VRAM**: 16GB minimum
- **Storage**: 100GB SSD
- **Network**: 10Gbps (for large audio files)

#### GPU Workers (LLM)
- **GPU**: NVIDIA A10, A100, or H100
- **CPU**: 16 cores
- **RAM**: 64GB
- **VRAM**: 24GB minimum (for 7B models)
- **Storage**: 200GB SSD
- **Network**: 10Gbps

#### Database Server
- **CPU**: 8 cores
- **RAM**: 32GB
- **Storage**: 500GB SSD (with backup)
- **Network**: 10Gbps
- **IOPS**: 3000+ (for high throughput)

#### Message Broker (RabbitMQ)
- **CPU**: 4 cores
- **RAM**: 16GB
- **Storage**: 100GB SSD
- **Network**: 1Gbps

## Docker Deployment

### Production Docker Compose

Create a `docker-compose.prod.yml` file:

```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - static_volume:/app/static
    depends_on:
      - web
    restart: unless-stopped

  web:
    image: your-registry/coda:latest
    command: gunicorn coda.wsgi:application --bind 0.0.0.0:8000 --workers 4
    volumes:
      - static_volume:/app/src/static
      - media_volume:/app/media
      - logs_volume:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=coda.settings.production
      - WORKER_TYPE=APP
    env_file:
      - .env.production
    depends_on:
      - db
      - rabbitmq
    restart: unless-stopped
    deploy:
      replicas: 2

  messenger:
    image: your-registry/coda:latest
    command: python manage.py messenger
    volumes:
      - logs_volume:/app/logs
    environment:
      - WORKER_TYPE=APP
    env_file:
      - .env.production
    depends_on:
      - db
      - rabbitmq
    restart: unless-stopped

  cpu-worker:
    image: your-registry/coda:latest
    command: celery -A coda.celery worker -l INFO -Q default --max-tasks-per-child=1000
    volumes:
      - logs_volume:/app/logs
    environment:
      - WORKER_TYPE=APP
    env_file:
      - .env.production
    depends_on:
      - db
      - rabbitmq
    restart: unless-stopped
    deploy:
      replicas: 3

  transcription-worker:
    image: your-registry/coda-transcription:latest
    command: celery -A coda.celery worker -l INFO --concurrency 1 -Q transcription --max-tasks-per-child=100
    volumes:
      - logs_volume:/app/logs
      - model_cache:/root/.cache
    environment:
      - WORKER_TYPE=TRANSCRIPTION
      - CT2_CUDA_ALLOW_FP16=1
    env_file:
      - .env.production
    depends_on:
      - db
      - rabbitmq
    restart: unless-stopped
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    deploy:
      replicas: 2

  llm-worker:
    image: your-registry/coda-llm:latest
    command: celery -A coda.celery worker -l INFO --concurrency 1 -Q summary,redflag --max-tasks-per-child=50
    volumes:
      - logs_volume:/app/logs
      - model_cache:/root/.cache
    environment:
      - WORKER_TYPE=GPU24
    env_file:
      - .env.production
    depends_on:
      - db
      - rabbitmq
    restart: unless-stopped
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    deploy:
      replicas: 2

  flower:
    image: your-registry/coda:latest
    command: celery -A coda.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - WORKER_TYPE=APP
    env_file:
      - .env.production
    depends_on:
      - rabbitmq
    restart: unless-stopped

  db:
    image: mysql:8.0
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${CODA_DB_NAME}
      - MYSQL_USER=${CODA_DB_USER}
      - MYSQL_PASSWORD=${CODA_DB_PASSWORD}
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  rabbitmq:
    image: rabbitmq:3.12-management
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASS}
    ports:
      - "15672:15672"  # Management UI
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  rabbitmq_data:
  redis_data:
  static_volume:
  media_volume:
  logs_volume:
  model_cache:
```

### Environment Configuration

Create `.env.production`:

```bash
# Django
DJANGO_SETTINGS_MODULE=coda.settings.production
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,api.your-domain.com

# Database
CODA_DB_NAME=coda_production
CODA_DB_USER=coda_user
CODA_DB_PASSWORD=secure_production_password
CODA_DB_HOST=db
CODA_DB_PORT=3306

# RabbitMQ
RABBITMQ_USER=coda_user
RABBITMQ_PASS=secure_rabbitmq_password
CELERY_BROKER_URL=amqp://coda_user:secure_rabbitmq_password@rabbitmq:5672//
RABBITMQ_URL=amqp://coda_user:secure_rabbitmq_password@rabbitmq:5672//messenger

# AWS
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
```

## Kubernetes Deployment

### Namespace and ConfigMap

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: coda-production
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: coda-config
  namespace: coda-production
data:
  DJANGO_SETTINGS_MODULE: "coda.settings.production"
  DEBUG: "False"
  LOG_LEVEL: "INFO"
  CODA_DB_HOST: "mysql-service"
  RABBITMQ_HOST: "rabbitmq-service"
```

### Web Application Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coda-web
  namespace: coda-production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: coda-web
  template:
    metadata:
      labels:
        app: coda-web
    spec:
      containers:
      - name: coda-web
        image: your-registry/coda:latest
        command: ["gunicorn", "coda.wsgi:application", "--bind", "0.0.0.0:8000", "--workers", "4"]
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: coda-config
        - secretRef:
            name: coda-secrets
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /codaapi/health/
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /codaapi/health/
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### GPU Worker Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coda-transcription-worker
  namespace: coda-production
spec:
  replicas: 2
  selector:
    matchLabels:
      app: coda-transcription-worker
  template:
    metadata:
      labels:
        app: coda-transcription-worker
    spec:
      nodeSelector:
        accelerator: nvidia-tesla-t4
      containers:
      - name: transcription-worker
        image: your-registry/coda-transcription:latest
        command: ["celery", "-A", "coda.celery", "worker", "-l", "INFO", "--concurrency", "1", "-Q", "transcription"]
        env:
        - name: WORKER_TYPE
          value: "TRANSCRIPTION"
        - name: CT2_CUDA_ALLOW_FP16
          value: "1"
        envFrom:
        - configMapRef:
            name: coda-config
        - secretRef:
            name: coda-secrets
        resources:
          requests:
            memory: "16Gi"
            cpu: "4000m"
            nvidia.com/gpu: 1
          limits:
            memory: "32Gi"
            cpu: "8000m"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: model-cache
          mountPath: /root/.cache
      volumes:
      - name: model-cache
        persistentVolumeClaim:
          claimName: model-cache-pvc
```

## CI/CD Pipeline

### GitLab CI Example

```yaml
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_REGISTRY: your-registry.com
  IMAGE_TAG: $CI_COMMIT_SHA

test:
  stage: test
  image: python:3.12
  services:
    - mysql:8.0
  variables:
    MYSQL_ROOT_PASSWORD: test
    MYSQL_DATABASE: test_coda
  script:
    - pip install -r requirements.txt
    - cd src
    - python manage.py test
  only:
    - merge_requests
    - main

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $DOCKER_REGISTRY
    - docker build -t $DOCKER_REGISTRY/coda:$IMAGE_TAG .
    - docker build --target transcription -t $DOCKER_REGISTRY/coda-transcription:$IMAGE_TAG .
    - docker build --target llm_generic -t $DOCKER_REGISTRY/coda-llm:$IMAGE_TAG .
    - docker push $DOCKER_REGISTRY/coda:$IMAGE_TAG
    - docker push $DOCKER_REGISTRY/coda-transcription:$IMAGE_TAG
    - docker push $DOCKER_REGISTRY/coda-llm:$IMAGE_TAG
  only:
    - main

deploy_staging:
  stage: deploy
  script:
    - ./scripts/deploy.sh staging $IMAGE_TAG
  environment:
    name: staging
    url: https://staging.your-domain.com
  only:
    - main

deploy_production:
  stage: deploy
  script:
    - ./scripts/deploy.sh production $IMAGE_TAG
  environment:
    name: production
    url: https://api.your-domain.com
  when: manual
  only:
    - main
```

## Database Migration Strategy

### Zero-Downtime Migrations

```bash
#!/bin/bash
# deploy.sh

set -e

ENVIRONMENT=$1
IMAGE_TAG=$2

echo "Deploying CODA to $ENVIRONMENT with image tag $IMAGE_TAG"

# 1. Update database schema (if needed)
docker run --rm \
  --env-file .env.$ENVIRONMENT \
  $DOCKER_REGISTRY/coda:$IMAGE_TAG \
  python manage.py migrate --check

# 2. Run migrations
docker run --rm \
  --env-file .env.$ENVIRONMENT \
  $DOCKER_REGISTRY/coda:$IMAGE_TAG \
  python manage.py migrate

# 3. Collect static files
docker run --rm \
  --env-file .env.$ENVIRONMENT \
  -v static_volume:/app/src/static \
  $DOCKER_REGISTRY/coda:$IMAGE_TAG \
  python manage.py collectstatic --noinput

# 4. Update services with rolling deployment
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d --no-deps web
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d --no-deps cpu-worker
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d --no-deps transcription-worker
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d --no-deps llm-worker

echo "Deployment completed successfully"
```

## Monitoring and Logging

### Prometheus Metrics

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'coda-web'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/metrics'

  - job_name: 'coda-flower'
    static_configs:
      - targets: ['flower:5555']
    metrics_path: '/metrics'

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
```

### Log Aggregation

```yaml
# filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
  - add_docker_metadata:
      host: "unix:///var/run/docker.sock"

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "coda-logs-%{+yyyy.MM.dd}"
```

## Security Considerations

### Network Security

```bash
# Firewall rules
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 5432/tcp   # Block direct database access
ufw deny 5672/tcp   # Block direct RabbitMQ access
```

### SSL/TLS Configuration

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://web:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Backup and Recovery

### Database Backup

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
docker exec mysql mysqldump \
  -u root -p$MYSQL_ROOT_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  coda_production > $BACKUP_DIR/coda_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/coda_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/coda_$DATE.sql.gz s3://your-backup-bucket/mysql/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

### Configuration Backup

```bash
#!/bin/bash
# backup-config.sh

# Backup Django configuration
docker exec web python manage.py dumpdata \
  service.ServiceConfiguration \
  worker.MessengerConfiguration \
  > /backups/config/service_config_$(date +%Y%m%d).json
```

## Scaling Strategies

### Horizontal Scaling

1. **Web Tier**: Add more web server instances behind load balancer
2. **Worker Tier**: Add more worker nodes based on queue depth
3. **Database Tier**: Implement read replicas for read-heavy workloads

### Auto-scaling with Kubernetes

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: coda-web-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: coda-web
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Troubleshooting

### Common Issues

1. **GPU Memory Issues**: Monitor VRAM usage and adjust batch sizes
2. **Queue Backlog**: Scale workers or optimize processing
3. **Database Locks**: Monitor slow queries and optimize indexes
4. **Network Timeouts**: Adjust timeout settings and connection pools

### Health Check Commands

```bash
# Check service health
curl http://localhost:8000/codaapi/health/

# Check worker status
docker exec web python manage.py shell -c "from worker.models import WorkerHost; print(WorkerHost.objects.all())"

# Check queue status
docker exec rabbitmq rabbitmqctl list_queues
```
