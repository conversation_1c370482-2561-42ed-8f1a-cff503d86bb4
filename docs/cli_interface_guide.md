# CODA CLI Interface User Guide

## Overview

The CODA CLI Interface provides command-line tools for managing, deploying, and operating the CODA system. It consists of two main components:
1. **Django Management Commands** - Python-based commands for application management
2. **CODA Shell Script** - Bash-based commands for Docker container management

## Django Management Commands

### Prerequisites
- Python environment with CODA dependencies installed
- Proper Django settings configuration
- Database connectivity

### Basic Usage
```bash
cd src/
python manage.py <command> [options]
```

### Available Commands

#### Core Django Commands
```bash
# Database management
python manage.py migrate                    # Apply database migrations
python manage.py showmigrations --plan     # Show pending migrations
python manage.py makemigrations            # Create new migrations

# User management
python manage.py createsuperuser          # Create admin user
python manage.py changepassword <username> # Change user password

# Development server
python manage.py runserver [host:port]     # Start development server
python manage.py collectstatic            # Collect static files

# Shell access
python manage.py shell                     # Django shell
python manage.py dbshell                  # Database shell
```

#### Custom CODA Commands

##### Worker Management
```bash
# Start messenger worker (RabbitMQ consumer)
python manage.py messenger

# Get job statistics
python manage.py get_job_stats --start-date YYYY-MM-DD --end-date YYYY-MM-DD
```

##### Service Management
```bash
# Add initial data (models, prompts, configurations)
python manage.py add_initial_data [--hf_token TOKEN]
```

##### Testing and Benchmarking
```bash
# Run transcription performance test
python manage.py transcription_test
```

### Command Options and Examples

#### Messenger Worker
```bash
# Start messenger worker with default settings
python manage.py messenger

# The messenger worker:
# - Consumes messages from RabbitMQ
# - Processes transcription, summarization, and red-flag requests
# - Manages workflow orchestration
# - Handles service dependencies
```

#### Job Statistics
```bash
# Get statistics for last 7 days
python manage.py get_job_stats --start-date 2024-01-01 --end-date 2024-01-07

# Output includes:
# - Workflow job statistics by service type
# - Average duration and job counts
# - Service job performance metrics
```

#### Initial Data Setup
```bash
# Add default configurations with HuggingFace token
python manage.py add_initial_data --hf_token your_hf_token_here

# This command adds:
# - Default LLM models and configurations
# - Prompt templates for summarization and red-flag analysis
# - Service configurations for transcription, summary, and red-flag services
```

## CODA Shell Script (coda.sh)

### Prerequisites
- Docker installed and running
- AWS CLI configured (for ECR access)
- Bash shell environment
- Appropriate permissions for Docker operations

### Basic Usage
```bash
./scripts/coda.sh <subcommand> [arguments...]
```

### Available Subcommands

#### 1. Run Applications
```bash
./scripts/coda.sh run <TAG> <APP> [OPTIONS]
```

**Available Applications:**
- `admin` - Django admin interface
- `admindev` - Development admin server
- `messenger` - Message processing worker
- `defaultworker` - CPU-based task worker
- `flower` - Celery monitoring interface
- `transcription` - GPU transcription worker
- `summaryredflag` - Combined summary/red-flag worker
- `summary` - Summary-only worker
- `redflag` - Red-flag-only worker
- `bash` - Interactive bash shell
- `migrate` - Database migration
- `getpendingmigrations` - Check pending migrations

**Common Options:**
- `--daemon, -d` - Run in detached mode
- `--interactive` - Run in interactive mode
- `--aws-region <REGION>` - Specify AWS region
- `--aws-account_id <ID>` - Specify AWS account ID
- `--log-folder <PATH>` - Custom log folder
- `--no-migration-check` - Skip migration check

**Examples:**
```bash
# Run admin interface with latest tag
./scripts/coda.sh run latest admin

# Run transcription worker in detached mode
./scripts/coda.sh run stable transcription -d

# Run with custom AWS settings
./scripts/coda.sh run latest admin --aws-region us-west-2 --aws-account_id *********
```

#### 2. Stop Applications
```bash
./scripts/coda.sh stop [APP] [OPTIONS]
```

**Options:**
- `--force, -f` - Force stop containers
- `--remove` - Remove containers after stopping

**Examples:**
```bash
# Stop all applications
./scripts/coda.sh stop all

# Stop specific application
./scripts/coda.sh stop admin

# Force stop and remove containers
./scripts/coda.sh stop messenger --force --remove
```

#### 3. Build Images
```bash
./scripts/coda.sh build [OPTIONS] [TAG] [IMAGE]
```

**Available Images:**
- `app` - Main application image
- `transcription` - Transcription worker image
- `llm_generic` - LLM worker image
- `all` - Build all images

**Options:**
- `-y` - Auto-confirm prompts
- `--force-rebuild` - Force rebuild without cache
- `--verbose` - Enable verbose output
- `--no-trivy-scan` - Skip security scan

**Examples:**
```bash
# Build all images with latest tag
./scripts/coda.sh build latest all

# Build specific image with verbose output
./scripts/coda.sh build --verbose stable app

# Force rebuild without cache
./scripts/coda.sh build --force-rebuild latest transcription
```

#### 4. Cleanup Resources
```bash
./scripts/coda.sh cleanup [OPTIONS]
```

**Options:**
- `--all` - Clean all resources (default)
- `--containers` - Clean stopped containers
- `--images` - Clean dangling images
- `--volumes` - Clean unused volumes
- `--networks` - Clean unused networks
- `--buildx` - Clean buildx cache

**Examples:**
```bash
# Clean all resources
./scripts/coda.sh cleanup --all

# Clean only volumes and images
./scripts/coda.sh cleanup --volumes --images
```

#### 5. Run Tests
```bash
./scripts/coda.sh test [OPTIONS]
```

**Examples:**
```bash
# Run all tests
./scripts/coda.sh test

# Run tests with specific configuration
./scripts/coda.sh test --verbose
```

#### 6. Wait for Initialization
```bash
./scripts/coda.sh wait-for-init <CONTAINER_NAME> [TIMEOUT]
```

**Examples:**
```bash
# Wait for admin container to initialize
./scripts/coda.sh wait-for-init admin 300
```

### Environment Configuration

#### Environment Variables
Key environment variables for CODA operation:

```bash
# Django settings
export DJANGO_SETTINGS_MODULE=coda.settings.staging

# Database configuration
export CODA_DB_HOST=localhost
export CODA_DB_PORT=3306
export CODA_DB_NAME=coda
export CODA_DB_USER=coda
export CODA_DB_PASSWORD=codapass

# RabbitMQ configuration
export RABBITMQ_URL=amqp://guest:guest@localhost:5672/
export CELERY_BROKER_URL=amqp://guest:guest@localhost:5672//

# AWS configuration
export AWS_REGION=us-east-1
export AWS_ACCOUNT_ID=************

# Worker configuration
export WORKER_TYPE=APP
```

#### Configuration Files
- `.env` - Environment variables for development
- `.env-compose` - Environment variables for Docker Compose
- `docker-compose.yml` - Docker service definitions

### Common Workflows

#### Development Setup
```bash
# 1. Build images
./scripts/coda.sh build latest all

# 2. Run database migrations
./scripts/coda.sh run latest migrate

# 3. Start admin interface
./scripts/coda.sh run latest admindev

# 4. Start workers
./scripts/coda.sh run latest messenger -d
./scripts/coda.sh run latest defaultworker -d
```

#### Production Deployment
```bash
# 1. Build production images
./scripts/coda.sh build stable all --no-trivy-scan

# 2. Check for pending migrations
./scripts/coda.sh run stable getpendingmigrations

# 3. Apply migrations if needed
./scripts/coda.sh run stable migrate

# 4. Start services
./scripts/coda.sh run stable admin -d
./scripts/coda.sh run stable messenger -d
./scripts/coda.sh run stable transcription -d
./scripts/coda.sh run stable summaryredflag -d
```

#### Maintenance Tasks
```bash
# Stop all services
./scripts/coda.sh stop all

# Clean up resources
./scripts/coda.sh cleanup --all

# Rebuild images
./scripts/coda.sh build --force-rebuild latest all

# Restart services
./scripts/coda.sh run latest admin -d
```

### Troubleshooting

#### Common Issues

##### Container Won't Start
```bash
# Check container logs
docker logs <container_name>

# Check if ports are available
netstat -tulpn | grep :8000

# Verify environment variables
./scripts/coda.sh run latest bash
env | grep CODA
```

##### Migration Issues
```bash
# Check pending migrations
./scripts/coda.sh run latest getpendingmigrations

# Apply migrations manually
./scripts/coda.sh run latest migrate

# Reset migrations (development only)
./scripts/coda.sh run latest bash
python manage.py migrate --fake-initial
```

##### Worker Issues
```bash
# Check worker status
./scripts/coda.sh run latest flower

# Restart workers
./scripts/coda.sh stop messenger
./scripts/coda.sh run latest messenger -d

# Check RabbitMQ connection
./scripts/coda.sh run latest bash
python manage.py shell
# Test RabbitMQ connectivity
```

#### Getting Help
```bash
# General help
./scripts/coda.sh help

# Subcommand help
./scripts/coda.sh run --help
./scripts/coda.sh build --help
./scripts/coda.sh stop --help

# Django command help
python manage.py help
python manage.py <command> --help
```

## Best Practices

### Development
1. Use `admindev` for development, `admin` for production
2. Always check for pending migrations before deployment
3. Use detached mode (`-d`) for background services
4. Monitor logs regularly with `docker logs`

### Production
1. Use stable tags for production deployments
2. Always run migration checks before updates
3. Use proper environment configurations
4. Implement proper backup strategies

### Security
1. Use secure environment variable management
2. Limit access to production systems
3. Regularly update base images
4. Monitor container security with Trivy scans

## Related Documentation
- [Admin Interface Guide](admin_interface_guide.md)
- [API Interface Guide](api_interface_guide.md)
- [Installation Guide](installation.md)
- [Docker Guide](docker.md)
