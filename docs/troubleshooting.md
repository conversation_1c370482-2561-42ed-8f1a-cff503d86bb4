# CODA Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered when running CODA, including system errors, performance problems, and configuration issues.

## Quick Diagnostics

### System Health Check

Start with the built-in health check:

```bash
# Check overall system health
curl http://localhost:8000/codaapi/health/

# Check worker status
curl http://localhost:8000/codaapi/health/workers/

# Check Django admin
curl http://localhost:8000/admin/

# Check Celery flower
curl http://localhost:5555/
```

### Service Status Commands

```bash
# Check Docker containers
docker ps -a

# Check container logs
docker logs coda-web
docker logs coda-transcription-worker
docker logs coda-llm-worker

# Check database connection
docker exec coda-web python manage.py dbshell

# Check RabbitMQ status
docker exec coda-rabbitmq rabbitmqctl status
docker exec coda-rabbitmq rabbitmqctl list_queues
```

## Common Issues and Solutions

### 1. Application Won't Start

#### Symptoms
- Django server fails to start
- Import errors in logs
- Database connection errors

#### Diagnosis
```bash
# Check Django configuration
python manage.py check

# Verify database connectivity
python manage.py dbshell

# Check for missing migrations
python manage.py showmigrations

# Verify environment variables
python manage.py shell -c "from django.conf import settings; print(settings.DATABASES)"
```

#### Solutions

**Missing Dependencies**
```bash
# Reinstall requirements
pip install -r requirements.txt

# Check for conflicting packages
pip check
```

**Database Issues**
```bash
# Run migrations
python manage.py migrate

# Create database if it doesn't exist
python manage.py migrate --run-syncdb
```

**Environment Variables**
```bash
# Check required environment variables
echo $DJANGO_SETTINGS_MODULE
echo $SECRET_KEY
echo $CODA_DB_HOST

# Load environment file
source .env.production
```

### 2. GPU Workers Not Starting

#### Symptoms
- GPU workers fail to start
- CUDA out of memory errors
- Model loading failures

#### Diagnosis
```bash
# Check GPU availability
nvidia-smi

# Check CUDA installation
python -c "import torch; print(torch.cuda.is_available())"

# Check GPU memory usage
nvidia-smi --query-gpu=memory.used,memory.total --format=csv

# Check Docker GPU runtime
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

#### Solutions

**NVIDIA Driver Issues**
```bash
# Check driver version
nvidia-smi

# Reinstall NVIDIA drivers
sudo apt purge nvidia-*
sudo apt install nvidia-driver-550-server
sudo reboot
```

**CUDA Memory Issues**
```bash
# Clear GPU memory
sudo nvidia-smi --gpu-reset

# Reduce batch size in configuration
# Edit service configuration in Django admin
# Set transcription_settings.batch_size to lower value (e.g., 8)
```

**Docker GPU Runtime**
```bash
# Install NVIDIA container toolkit
sudo apt install nvidia-container-toolkit
sudo systemctl restart docker

# Test GPU access
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

### 3. High Queue Backlog

#### Symptoms
- Tasks accumulating in queues
- Slow processing times
- Workers appear idle

#### Diagnosis
```bash
# Check queue depths
docker exec coda-rabbitmq rabbitmqctl list_queues

# Check worker status
celery -A coda.celery inspect active
celery -A coda.celery inspect stats

# Check worker logs
docker logs coda-transcription-worker
docker logs coda-llm-worker
```

#### Solutions

**Scale Workers**
```bash
# Add more worker instances
docker-compose up -d --scale transcription-worker=3
docker-compose up -d --scale llm-worker=2

# Or start additional workers manually
docker run -d --name transcription-worker-2 \
  --gpus all \
  --env-file .env.production \
  your-registry/coda-transcription:latest
```

**Optimize Worker Configuration**
```bash
# Increase concurrency for CPU workers
celery -A coda.celery worker -l INFO --concurrency=8 -Q default

# Adjust prefetch settings
# Add to celery configuration:
# CELERY_WORKER_PREFETCH_MULTIPLIER = 1
```

**Clear Stuck Tasks**
```bash
# Purge specific queue (use with caution)
docker exec coda-rabbitmq rabbitmqctl purge_queue transcription

# Restart workers to clear stuck tasks
docker restart coda-transcription-worker
```

### 4. Database Performance Issues

#### Symptoms
- Slow API responses
- Database connection timeouts
- High CPU usage on database server

#### Diagnosis
```bash
# Check database connections
docker exec coda-db mysql -e "SHOW PROCESSLIST;"

# Check slow queries
docker exec coda-db mysql -e "SHOW VARIABLES LIKE 'slow_query_log';"

# Check table locks
docker exec coda-db mysql -e "SHOW OPEN TABLES WHERE In_use > 0;"

# Monitor database performance
docker exec coda-db mysqladmin -i 1 status
```

#### Solutions

**Optimize Queries**
```sql
-- Add indexes for frequently queried fields
CREATE INDEX idx_workflow_job_status ON worker_workflowjob(status);
CREATE INDEX idx_service_job_parley ON worker_servicejob(parley_id);
CREATE INDEX idx_worker_host_status ON worker_workerhost(status);
```

**Connection Pool Tuning**
```python
# In Django settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
        'CONN_MAX_AGE': 3600,  # Connection pooling
        'CONN_HEALTH_CHECKS': True,
    }
}
```

**Database Maintenance**
```bash
# Optimize tables
docker exec coda-db mysqlcheck --optimize --all-databases

# Analyze tables
docker exec coda-db mysqlcheck --analyze --all-databases

# Check table sizes
docker exec coda-db mysql -e "
SELECT 
    table_schema,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
ORDER BY (data_length + index_length) DESC;
"
```

### 5. RabbitMQ Issues

#### Symptoms
- Message broker connection failures
- Tasks not being delivered
- High memory usage

#### Diagnosis
```bash
# Check RabbitMQ status
docker exec coda-rabbitmq rabbitmqctl status

# Check memory usage
docker exec coda-rabbitmq rabbitmqctl status | grep memory

# Check connections
docker exec coda-rabbitmq rabbitmqctl list_connections

# Check exchanges and bindings
docker exec coda-rabbitmq rabbitmqctl list_exchanges
docker exec coda-rabbitmq rabbitmqctl list_bindings
```

#### Solutions

**Memory Issues**
```bash
# Set memory high watermark
docker exec coda-rabbitmq rabbitmqctl set_vm_memory_high_watermark 0.6

# Clear memory by restarting
docker restart coda-rabbitmq
```

**Connection Issues**
```bash
# Check network connectivity
docker exec coda-web ping rabbitmq

# Verify credentials
docker exec coda-rabbitmq rabbitmqctl list_users

# Reset user password
docker exec coda-rabbitmq rabbitmqctl change_password coda_user new_password
```

**Queue Management**
```bash
# List all queues with details
docker exec coda-rabbitmq rabbitmqctl list_queues name messages consumers

# Purge specific queue
docker exec coda-rabbitmq rabbitmqctl purge_queue transcription

# Delete and recreate queue
docker exec coda-rabbitmq rabbitmqctl delete_queue transcription
```

### 6. Storage and File Issues

#### Symptoms
- File download failures
- S3 connection errors
- Disk space issues

#### Diagnosis
```bash
# Check disk space
df -h

# Check Docker volume usage
docker system df

# Test S3 connectivity
aws s3 ls s3://your-bucket-name/

# Check file permissions
ls -la /var/log/coda/
```

#### Solutions

**Disk Space**
```bash
# Clean up Docker
docker system prune -a

# Clean up logs
sudo find /var/log -name "*.log" -type f -mtime +30 -delete

# Rotate logs
sudo logrotate -f /etc/logrotate.conf
```

**S3 Issues**
```bash
# Test AWS credentials
aws sts get-caller-identity

# Check S3 permissions
aws s3api get-bucket-policy --bucket your-bucket-name

# Test file access
aws s3 cp s3://your-bucket/test-file.wav /tmp/test.wav
```

### 7. Performance Optimization

#### Slow Transcription

**Diagnosis**
```bash
# Check GPU utilization
nvidia-smi -l 1

# Monitor transcription logs
docker logs -f coda-transcription-worker | grep "duration"

# Check batch size configuration
# Via Django admin: Service Configuration -> transcription_settings.batch_size
```

**Solutions**
```bash
# Optimize batch size
# Increase for better GPU utilization: 16, 24, 32
# Decrease if running out of memory: 8, 4

# Use FP16 precision
export CT2_CUDA_ALLOW_FP16=1

# Optimize audio preprocessing
# Set appropriate sample rate in configuration
```

#### Slow LLM Inference

**Diagnosis**
```bash
# Check model loading time
docker logs coda-llm-worker | grep "model.*loaded"

# Monitor inference time
docker logs coda-llm-worker | grep "inference.*completed"

# Check GPU memory usage
nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits
```

**Solutions**
```bash
# Use quantized models
# Configure load_in_8bit: true in service configuration

# Optimize generation parameters
# Reduce max_length, adjust temperature

# Use model caching
# Keep models loaded between tasks
```

## Emergency Procedures

### System Recovery

**Complete System Restart**
```bash
# Stop all services
docker-compose down

# Clean up containers and networks
docker system prune

# Restart services in order
docker-compose up -d db rabbitmq
sleep 30
docker-compose up -d web messenger
sleep 10
docker-compose up -d cpu-worker transcription-worker llm-worker
```

**Database Recovery**
```bash
# Stop application services
docker-compose stop web messenger cpu-worker transcription-worker llm-worker

# Backup current database
docker exec coda-db mysqldump -u root -p coda_production > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
docker exec -i coda-db mysql -u root -p coda_production < latest_backup.sql

# Restart services
docker-compose up -d
```

**Queue Recovery**
```bash
# Stop all workers
docker-compose stop cpu-worker transcription-worker llm-worker

# Clear all queues
docker exec coda-rabbitmq rabbitmqctl purge_queue default
docker exec coda-rabbitmq rabbitmqctl purge_queue transcription
docker exec coda-rabbitmq rabbitmqctl purge_queue summary
docker exec coda-rabbitmq rabbitmqctl purge_queue redflag

# Restart workers
docker-compose up -d cpu-worker transcription-worker llm-worker
```

## Monitoring and Alerting

### Key Metrics to Watch

1. **Queue Depths**: Should be < 100 under normal load
2. **Worker Health**: All workers should be "healthy"
3. **Response Times**: API responses < 2 seconds
4. **Error Rates**: < 1% error rate
5. **GPU Memory**: < 90% utilization
6. **Database Connections**: < 80% of max connections

### Alert Thresholds

```yaml
# Example alert configuration
alerts:
  - name: high_queue_depth
    condition: queue_depth > 1000
    severity: warning
    
  - name: worker_offline
    condition: healthy_workers < 3
    severity: critical
    
  - name: high_error_rate
    condition: error_rate > 0.05
    severity: warning
    
  - name: gpu_memory_high
    condition: gpu_memory_usage > 0.9
    severity: warning
```

## Getting Help

### Log Locations

```bash
# Application logs
/var/log/coda/
docker logs coda-web
docker logs coda-transcription-worker

# System logs
/var/log/syslog
journalctl -u docker

# Database logs
docker logs coda-db

# RabbitMQ logs
docker logs coda-rabbitmq
```

### Useful Commands

```bash
# System information
uname -a
docker version
nvidia-smi
free -h
df -h

# Network diagnostics
netstat -tulpn
ss -tulpn
ping google.com

# Process monitoring
top
htop
ps aux | grep python
```

### Support Contacts

- **System Issues**: Contact DevOps team
- **Application Bugs**: Create issue in GitLab
- **Performance Issues**: Contact development team
- **Infrastructure**: Contact infrastructure team

### Documentation References

- [Architecture Guide](./architecture.md)
- [Configuration Guide](./configuration.md)
- [Monitoring Guide](./monitoring.md)
- [API Documentation](./api.md)
