# CODA API Interface User Guide

## Overview

The CODA API Interface provides RESTful endpoints for programmatic access to CODA system functionality. Built with Django REST Framework, it offers comprehensive access to job management, worker monitoring, and LLM inference capabilities.

## API Documentation Access

### Interactive Documentation
- **Swagger UI**: `http://your-domain:8000/codaapi/docs/`
- **ReDoc**: `http://your-domain:8000/codaapi/redoc/`
- **OpenAPI Schema**: `http://your-domain:8000/codaapi/schema/`

### Base URL
All API endpoints are prefixed with: `http://your-domain:8000/codaapi/`

## Authentication

Currently, the API uses Django's built-in authentication system. Some endpoints are public (health checks), while others may require authentication depending on configuration.

## Core API Endpoints

### 1. Health Check Endpoints

#### System Health Check
```http
GET /codaapi/health/
```

**Purpose**: Check overall system health status

**Response Example**:
```json
{
  "status": "ok",
  "database": "ok",
  "rabbitmq": "ok",
  "consumers": "ok"
}
```

**Response Fields**:
- `status`: Overall system status
- `database`: Database connectivity status
- `rabbitmq`: Message queue status
- `consumers`: Worker consumer status

#### Worker Health Check
```http
GET /codaapi/health/workers/
```

**Purpose**: Check worker node health and status

**Response Example**:
```json
[
  {
    "id": 1,
    "name": "worker-01",
    "hostname": "worker-01.local",
    "status": "online",
    "healthy": true,
    "ping_response": "pong",
    "down_for_maintenance": false
  }
]
```

### 2. Workflow Job Management

#### List Workflow Jobs
```http
GET /codaapi/workflow-jobs/
```

**Purpose**: Retrieve list of workflow jobs with filtering and pagination

**Query Parameters**:
- `status`: Filter by job status (pending, started, completed, failed)
- `language`: Filter by language code
- `parley_id`: Filter by specific parley ID
- `customer_id`: Filter by customer ID
- `ordering`: Sort results (e.g., `-started_at`, `completed_at`)
- `page`: Page number for pagination

**Example Request**:
```http
GET /codaapi/workflow-jobs/?status=completed&customer_id=100069&ordering=-started_at
```

**Response Example**:
```json
{
  "count": 150,
  "next": "http://your-domain:8000/codaapi/workflow-jobs/?page=2",
  "previous": null,
  "results": [
    {
      "id": 123,
      "parley_id": 100,
      "customer_id": 100069,
      "language": "en",
      "requested_services": "transcribe,summarise",
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "started_at": "2024-01-15T10:30:05Z",
      "completed_at": "2024-01-15T10:32:15Z",
      "service_jobs": [1, 2, 3]
    }
  ]
}
```

#### Get Workflow Job Details
```http
GET /codaapi/workflow-jobs/{id}/
```

**Purpose**: Retrieve detailed information about a specific workflow job

**Response Example**:
```json
{
  "id": 123,
  "parley_id": 100,
  "customer_id": 100069,
  "language": "en",
  "requested_services": "transcribe,summarise",
  "status": "completed",
  "created_at": "2024-01-15T10:30:00Z",
  "started_at": "2024-01-15T10:30:05Z",
  "completed_at": "2024-01-15T10:32:15Z",
  "duration": "00:02:10",
  "service_jobs": [
    {
      "id": 1,
      "name": "transcribe",
      "type": "transcription",
      "status": "completed"
    },
    {
      "id": 2,
      "name": "summarise",
      "type": "summary",
      "status": "completed"
    }
  ]
}
```

### 3. Service Job Management

#### List Service Jobs
```http
GET /codaapi/service-jobs/
```

**Purpose**: Retrieve list of individual service jobs

**Query Parameters**:
- `type`: Filter by job type (transcription, summary, redflag)
- `status`: Filter by job status
- `workflow`: Filter by workflow ID
- `worker`: Filter by worker ID
- `ordering`: Sort results

**Example Request**:
```http
GET /codaapi/service-jobs/?type=transcription&status=completed&ordering=-completed_at
```

**Response Example**:
```json
{
  "count": 75,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "workflow": 123,
      "name": "transcribe",
      "type": "transcription",
      "status": "completed",
      "worker": {
        "id": 1,
        "name": "transcription-worker-01"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "started_at": "2024-01-15T10:30:05Z",
      "completed_at": "2024-01-15T10:31:45Z",
      "duration": "00:01:40"
    }
  ]
}
```

#### Get Service Job Details
```http
GET /codaapi/service-jobs/{id}/
```

**Purpose**: Retrieve detailed information about a specific service job

### 4. LLM Inference Jobs

#### List Inference Jobs
```http
GET /codaapi/inference-jobs/
```

**Purpose**: Retrieve list of LLM inference jobs

**Response Example**:
```json
{
  "count": 25,
  "results": [
    {
      "id": 1,
      "task_id": "celery-task-uuid",
      "input_data": {
        "prompt": "Summarize this text...",
        "context": "..."
      },
      "generation_config": {
        "max_tokens": 1024,
        "temperature": 0.7
      },
      "result": {
        "generated_text": "Summary of the input text...",
        "metadata": {}
      },
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "started_at": "2024-01-15T10:30:05Z",
      "completed_at": "2024-01-15T10:30:45Z"
    }
  ]
}
```

#### Create Inference Job
```http
POST /codaapi/inference-jobs/
```

**Purpose**: Create a new LLM inference job

**Request Body**:
```json
{
  "input_data": {
    "prompt": "Please summarize the following text:",
    "text": "Long text to be summarized..."
  },
  "generation_config": {
    "max_tokens": 512,
    "temperature": 0.3,
    "top_p": 0.9
  }
}
```

**Response Example**:
```json
{
  "id": 26,
  "task_id": "new-celery-task-uuid",
  "input_data": {
    "prompt": "Please summarize the following text:",
    "text": "Long text to be summarized..."
  },
  "generation_config": {
    "max_tokens": 512,
    "temperature": 0.3,
    "top_p": 0.9
  },
  "result": null,
  "status": "pending",
  "created_at": "2024-01-15T11:00:00Z",
  "started_at": null,
  "completed_at": null
}
```

#### Get Inference Job Details
```http
GET /codaapi/inference-jobs/{id}/
```

**Purpose**: Retrieve detailed information about a specific inference job

#### Update Inference Job
```http
PUT /codaapi/inference-jobs/{id}/
PATCH /codaapi/inference-jobs/{id}/
```

**Purpose**: Update inference job configuration (triggers re-execution)

## API Usage Examples

### Python Examples

#### Health Check
```python
import requests

# Check system health
response = requests.get('http://localhost:8000/codaapi/health/')
health_data = response.json()
print(f"System status: {health_data['status']}")

# Check worker health
response = requests.get('http://localhost:8000/codaapi/health/workers/')
workers = response.json()
for worker in workers:
    print(f"Worker {worker['name']}: {'healthy' if worker['healthy'] else 'unhealthy'}")
```

#### Job Monitoring
```python
import requests

# Get recent workflow jobs
params = {
    'status': 'completed',
    'ordering': '-completed_at',
    'page_size': 10
}
response = requests.get('http://localhost:8000/codaapi/workflow-jobs/', params=params)
jobs = response.json()

for job in jobs['results']:
    print(f"Job {job['id']}: {job['requested_services']} - {job['status']}")
```

#### Create Inference Job
```python
import requests
import time

# Create inference job
job_data = {
    'input_data': {
        'prompt': 'Summarize this meeting transcript:',
        'text': 'Meeting transcript content here...'
    },
    'generation_config': {
        'max_tokens': 256,
        'temperature': 0.5
    }
}

response = requests.post('http://localhost:8000/codaapi/inference-jobs/', json=job_data)
job = response.json()
job_id = job['id']

# Poll for completion
while True:
    response = requests.get(f'http://localhost:8000/codaapi/inference-jobs/{job_id}/')
    job = response.json()
    
    if job['status'] in ['completed', 'failed']:
        break
    
    time.sleep(5)

if job['status'] == 'completed':
    print(f"Result: {job['result']['generated_text']}")
else:
    print(f"Job failed: {job.get('error', 'Unknown error')}")
```

### cURL Examples

#### Health Check
```bash
# System health
curl -X GET http://localhost:8000/codaapi/health/

# Worker health
curl -X GET http://localhost:8000/codaapi/health/workers/
```

#### Job Queries
```bash
# Get workflow jobs with filters
curl -X GET "http://localhost:8000/codaapi/workflow-jobs/?status=completed&customer_id=100069"

# Get specific workflow job
curl -X GET http://localhost:8000/codaapi/workflow-jobs/123/

# Get service jobs
curl -X GET "http://localhost:8000/codaapi/service-jobs/?type=transcription&status=completed"
```

#### Create Inference Job
```bash
curl -X POST http://localhost:8000/codaapi/inference-jobs/ \
  -H "Content-Type: application/json" \
  -d '{
    "input_data": {
      "prompt": "Summarize this text:",
      "text": "Text to summarize..."
    },
    "generation_config": {
      "max_tokens": 256,
      "temperature": 0.7
    }
  }'
```

## Error Handling

### HTTP Status Codes
- `200 OK`: Successful request
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

### Error Response Format
```json
{
  "error": "Error message",
  "details": {
    "field": ["Field-specific error message"]
  }
}
```

## Rate Limiting and Best Practices

### Best Practices
1. **Pagination**: Use pagination for large result sets
2. **Filtering**: Apply filters to reduce response size
3. **Polling**: Use reasonable intervals when polling job status
4. **Error Handling**: Implement proper error handling and retries
5. **Caching**: Cache responses when appropriate

### Performance Tips
1. Use specific filters to reduce query load
2. Implement client-side caching for static data
3. Use appropriate page sizes for pagination
4. Monitor API response times and adjust accordingly

## Integration Examples

### Monitoring Dashboard
```python
import requests
import time
from datetime import datetime, timedelta

class CODAMonitor:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
    
    def get_system_health(self):
        response = requests.get(f'{self.base_url}/health/')
        return response.json()
    
    def get_worker_status(self):
        response = requests.get(f'{self.base_url}/health/workers/')
        return response.json()
    
    def get_recent_jobs(self, hours=24):
        since = datetime.now() - timedelta(hours=hours)
        params = {
            'created_at__gte': since.isoformat(),
            'ordering': '-created_at'
        }
        response = requests.get(f'{self.base_url}/workflow-jobs/', params=params)
        return response.json()
    
    def monitor_loop(self):
        while True:
            health = self.get_system_health()
            workers = self.get_worker_status()
            
            print(f"System Health: {health['status']}")
            print(f"Healthy Workers: {sum(1 for w in workers if w['healthy'])}/{len(workers)}")
            
            time.sleep(60)

# Usage
monitor = CODAMonitor('http://localhost:8000/codaapi')
monitor.monitor_loop()
```

## Related Documentation
- [Admin Interface Guide](admin_interface_guide.md)
- [CLI Interface Guide](cli_interface_guide.md)
- [Installation Guide](installation.md)
- [Developer Guide](dev_guide.md)
