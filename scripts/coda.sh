#!/bin/bash

# -------------------------------------------------------
# coda - A base script with two subcommands:
#   1) run  - Provides the functionality of run-coda.sh.
#   2) foo  - An empty (placeholder) subcommand for future use.
#
# Usage:
#   ./coda <subcommand> [arguments...]
#
# Examples:
#   ./coda run <TAG> <APP> [--ecr-base-url ...] [--log-folder ...] ...
#   ./coda foo
#
# Add additional commands or expand the 'foo' subcommand as needed.
# -------------------------------------------------------

function run_help {
  echo -e "\n\033[1mrun-coda.sh\033[0m - A script to run a Docker container for the specified application."
  echo
  echo -e "\033[1mRequirements:\033[0m"
  echo "  - awscli: Required to fetch the Docker ECR login credentials."
  echo "  - Docker: Required to pull and run Docker containers."
  echo
  echo -e "\033[1mUsage:\033[0m"
  echo "  ./run-coda.sh <TAG> <APP> [OPTIONS]"
  echo
  echo -e "\033[1mArguments:\033[0m"
  echo -e "  \033[1mTAG\033[0m:"
  echo "      The Docker image tag to use (e.g., 'latest', 'stable', etc.)."
  echo -e "  \033[1mAPP\033[0m:"
  echo "      The application name you want to run."
  echo "      Valid options include: admin, messenger, defaultworker, summaryapi, redflagapi, summaryredflagapi, flower, transcription, summaryredflag, bash, migrate, getpendingmigrations."
  echo
  echo -e "\033[1mOptions:\033[0m"
  echo "  --aws-region <REGION>               Specify the AWS region."
  echo "                                  Optional. Defaults to: '$DEFAULT_REGION'."
  echo "  --aws-account_id <ACCOUNT_ID>       Specify the AWS account ID."
  echo "                                  Optional. Defaults to: '$DEFAULT_ACCOUNT_ID'."
  echo "  --ecr-base_repo <BASE_REPO>         Specify the base repository."
  echo "                                  Optional. Defaults to: '$DEFAULT_BASE_REPO'."
  echo "  --log-folder <LOG_FOLDER>       Specify a custom log folder."
  echo "                                  Defaults to: '$DEFAULT_LOG_FOLDER'."
  echo "  --uid <USER_UID>                Override the default UID (User ID) used by the script."
  echo "                                  Defaults to the current user's UID or SUDO_UID when running with sudo."
  echo "  --gid <USER_GID>                Override the default GID (Group ID) used by the script."
  echo "                                  Defaults to the current user's GID or SUDO_GID when running with sudo."
  echo "  --no-migration-check            Disable the migration check if this option is provided."
  echo "  --interactive                   Run the containers in interactive mode (-it)."
  echo "  --daemon, -d                    Run the container in detached mode."
  echo "                                  If omitted, the container will run interactively with '-it'."
  echo "  --help, -h                      Display this help message."
  echo
  echo -e "\033[1mExamples:\033[0m"
  echo "  Run the admin application with the 'stable' tag:"
  echo "    ./run-coda.sh stable admin"
  echo "  Run the transcription application in detached mode:"
  echo "    ./run-coda.sh stable transcription -d"
  echo "  Use a custom log folder and specify AWS region, account ID, and base repository:"
  echo "    ./run-coda.sh stable admin --log-folder /path/to/logs --region your_region --account_id your_account_id --base_repo your_base_repo"
  echo "  Override UID and GID for the script:"
  echo "    ./run-coda.sh stable admin --uid 1001 --gid 1001"
  echo -e "\033[1mNotes:\033[0m"
  echo "  - TAG and APP arguments are required; the script will fail if they are missing."
  echo "  - To view logs for detached mode, use 'docker logs <container_name>'."
  echo "  - Use './run-coda.sh --help' or './run-coda.sh -h' to display this message at any time."
  exit 0
}

# The function that implements the functionality of run-coda.sh
function coda_run {
  # If help flag is the first argument
  if [[ "$1" == "--help" || "$1" == "-h" ]]; then
      # Display run_help or replicate help output from run-coda.sh
      run_help
      exit 0
  fi

  # Preserve the original user's home directory when running with sudo
  if [ -n "$SUDO_USER" ]; then
    USER_HOME=$(eval echo "~$SUDO_USER")
  else
    USER_HOME="$HOME"
  fi

  # Use the actual user's UID and GID if running with sudo
  if [ -n "$SUDO_UID" ]; then
    USER_UID="$SUDO_UID"
    USER_GID="$SUDO_GID"
  else
    USER_UID=$(id -u)
    USER_GID=$(id -g)
  fi

  # Defaults
  DJANGO_SETTINGS_MODULE=coda.settings.staging  # this defines the execution environment and defaults to STAGING
  DEFAULT_LOG_FOLDER="$USER_HOME/coda_logs"
  DEFAULT_REGION="us-east-1"
  DEFAULT_ACCOUNT_ID="************"
  DEFAULT_BASE_REPO="symphony-staging/coda"
  REGION="$DEFAULT_REGION"
  ACCOUNT_ID="$DEFAULT_ACCOUNT_ID"
  BASE_REPO="$DEFAULT_BASE_REPO"
  LOG_FOLDER="$DEFAULT_LOG_FOLDER"
  NO_MIGRATION_CHECK=false
  RUN_AS_DAEMON=false
  INTERACTIVE_MODE=""

  # Make sure TAG and APP are provided
  if [ -z "$1" ] || [ -z "$2" ]; then
      echo "Error: Both TAG and APP parameters are required for the 'run' subcommand."
      echo "Use 'coda run --help' for more information."
      exit 1
  fi

  # Read required parameters
  TAG=$1
  shift

  # Collect application names until we reach an option (e.g., "--something" or "-d")
  APPS=()
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --*|-*)
        # We’ve encountered an option; stop collecting app names
        break
        ;;
      *)
        # This is an app name
        APPS+=("$1")
        shift
        ;;
    esac
  done

  # Check for at least one app
  if [ ${#APPS[@]} -eq 0 ]; then
    echo "Error: You must specify at least one application."
    exit 1
  fi

  # Process remaining arguments (example)
  while (( "$#" )); do
    case "$1" in
      --run-production)
        DJANGO_SETTINGS_MODULE=coda.settings.production
        shift
        ;;
      --aws-region)
        REGION="$2"
        shift 2
        ;;
      --aws-account_id)
        ACCOUNT_ID="$2"
        shift 2
        ;;
      --ecr-base_repo)
        BASE_REPO="$2"
        shift 2
        ;;
      --log-folder)
        LOG_FOLDER="$2"
        shift 2
        ;;
      --no-migration-check)
        NO_MIGRATION_CHECK=true
        shift
        ;;
      --interactive)
        INTERACTIVE_MODE="-it"
        shift
        ;;
      --daemon|-d)
        RUN_AS_DAEMON=true
        shift
        ;;
      --uid)
        USER_UID="$2"
        shift 2
        ;;
      --gid)
        USER_GID="$2"
        shift 2
        ;;
      *)
        echo "Unknown option or argument: $1"
        exit 1
        ;;
    esac
  done

  ECR_BASE_URL="$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$BASE_REPO"

  # Retrieve AWS ECR login password
  LOGIN_PASSWORD=$(/usr/local/bin/aws ecr get-login-password --region us-east-1)
  if [ $? -ne 0 ]; then
    echo "Failed to retrieve AWS ECR login password."
    exit 1
  fi

  echo "$LOGIN_PASSWORD" | docker login --username AWS --password-stdin "$ECR_BASE_URL"

  # Step: Check for pending migrations unless --no-migration-check is specified
  if [ "$NO_MIGRATION_CHECK" = false ]; then
    echo "Checking for pending migrations..."
    set -o pipefail
    pending_migrations=$(docker run --pull=always --network host -e "DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS_MODULE" --rm "$ECR_BASE_URL/app:$TAG" getpendingmigrations | grep -E '^\[ \]' || true)
    if [ $? -ne 0 ]; then
      echo "Error occurred while checking for pending migrations."
      exit 1
    fi
    set +o pipefail

    if [ -z "$pending_migrations" ]; then
      echo "No pending migrations found."
    else
      echo "Pending migrations detected:"
      echo "$pending_migrations"
      echo "Apply the migrations before continuing."
      exit 1
    fi
  else
    echo "Skipping migration check (due to --no-migration-check flag)."
  fi

  setup_log_folder() {
    local log_folder="$1"  # Accept the folder as the first argument

    mkdir -p "$log_folder"
    chown -R "$USER_UID:$USER_GID" "$log_folder"
    chmod -R 740 "$log_folder"
  }

  # Ensure the log folder exists and set appropriate permissions
#  mkdir -p "$LOG_FOLDER"
#  chown -R "$USER_UID:$USER_GID" "$LOG_FOLDER"
#  chmod -R 740 "$LOG_FOLDER"

  setup_log_folder "$LOG_FOLDER"


  COMMON_SETTINGS="--user $USER_UID:$USER_GID --network host -e "DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS_MODULE" -e HOSTNAME=$(hostname) -e HOST_IP=$(hostname -i)"

  # Set DOCKER_RUN_MODE based on the -d flag
  if [ "$RUN_AS_DAEMON" = true ]; then
    DOCKER_RUN_MODE="-d $COMMON_SETTINGS" # Detached mode
  else
    DOCKER_RUN_MODE="$INTERACTIVE_MODE $COMMON_SETTINGS"
  fi


  COMMON_GPU_SETTINGS="
    --runtime=nvidia \
    --gpus all \
    -v $USER_HOME/.cache/huggingface/hub:/home/<USER>/.cache/huggingface/hub \
    -v $USER_HOME/.cache/torch:/home/<USER>/.cache/torch"

  echo "Running coda container(s) with:"
  echo "  TAG:       $TAG"
  echo "  APP:       $APPS"
  echo "  ECR:       $ECR_BASE_URL"
  echo "  LOG_FOLDER $LOG_FOLDER"
  echo "  USER_UID:  $USER_UID"
  echo "  USER_GID:  $USER_GID"
  echo

  # Enforce daemon mode if more than one app is specified
  if [ ${#APPS[@]} -gt 1 ] && [ "$RUN_AS_DAEMON" = false ]; then
    echo "Error: Multiple apps can only be run in daemon mode. Use --daemon or -d."
    exit 1
  fi


  function pull_from_ecr {
    if [ "$NO_MIGRATION_CHECK" = true ]; then
      echo "Pulling the latest image..."
      docker pull "$ECR_BASE_URL/app:$TAG"
      if [ $? -ne 0 ]; then
        echo "Failed to pull the Docker image: $ECR_BASE_URL/app:$TAG"
        exit 1
      fi
    fi
  }

  NGINX_PORT_FORWARDING="-p 80:80 -p 443:443"

  # From here, you can loop over the APPS array as needed
  for APP in "${APPS[@]}"; do
    echo "Starting $APP..."
    # Validate that the admin app requires daemon mode
    if [ "$APP" = "admin" ] && [ "$RUN_AS_DAEMON" = false ]; then
      echo "Error: The 'admin' application can only be run in daemon mode. Use the --daemon or -d flag."
      echo "       Alternatively use the 'admindev' application"
      exit 1
    fi

    # Docker run command based on the APP type
    case "$APP" in
      # General-purpose apps
      bash|migrate|getpendingmigrations)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --rm \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" "$APP"
        ;;

      messenger)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" "$APP"
        ;;

      defaultworker)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" "celery" "-A" "coda.celery" "worker" "-l" "INFO" "-Q" "default" "--concurrency=1" "$APP"
        ;;

      summaryapi)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" celery -A coda.celery worker -l INFO -Q summary --concurrency=5 defaultworker
        ;;

      redflagapi)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" celery -A coda.celery worker -l INFO -Q redflag --concurrency=5 defaultworker
        ;;

      summaryredflagapi)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" "celery" "-A" "coda.celery" "worker" "-l" "INFO" "-Q" "summary,redflag" "--concurrency=5" "defaultworker"
        ;;

      flower)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -p 8001:8001 \
          -e WORKER_TYPE=APP \
          -e FLOWER_UNAUTHENTICATED_API=true \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" "$APP" "--url_prefix=flower"
        ;;

      admin)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run --pull=always $DOCKER_RUN_MODE \
          --name nginx \
          --restart unless-stopped \
          $NGINX_PORT_FORWARDING \
          -v $LOG_FOLDER/nginx:/var/log/nginx \
          -v $USER_HOME/static:/usr/share/nginx/static \
          "$ECR_BASE_URL/nginx:$TAG"
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -p 8000:8000 \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" "$APP"
        ;;

      admingunicorn)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -p 8000:8000 \
          -e WORKER_TYPE=APP \
          -v $USER_HOME/static:/app/src/static \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" admin
        ;;

      admindev)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        pull_from_ecr
        docker run $DOCKER_RUN_MODE \
          --name "$APP" \
          --rm \
          -p 8000:8000 \
          -e WORKER_TYPE=APP \
          -e DEBUG=true \
          --entrypoint /usr/bin/python \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/app:$TAG" manage.py runserver 0.0.0.0:8000
        ;;

      test)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP_$TAG" || true
        docker rm "$APP_$TAG" || true
        DOCKER_CMD="docker run --pull=always $DOCKER_RUN_MODE \
          --name \"$APP_$TAG\" \
          --rm \
          -e WORKER_TYPE=APP \
          -e DEBUG=true \
          -e DJANGO_SETTINGS_MODULE=coda.settings.test \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          -v /home/<USER>/jenkins_home/workspace/codapipeline/test-results:/app/src/test-results \
          --entrypoint /bin/bash \
          \"$ECR_BASE_URL/$APP:$TAG\" -c \"sudo chmod 777 /app/src/test-results && sudo pytest --cov=. --junitxml=/app/src/test-results/test-results.xml .\""
        echo "About to execute: $DOCKER_CMD"
        eval $DOCKER_CMD
        ;;
      testlint)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP_$TAG_lintint" || true
        docker rm "$APP_$TAG_lintint" || true
        docker run --pull=always $DOCKER_RUN_MODE \
          --name "$APP_$TAG_lintint" \
          --rm \
          -e WORKER_TYPE=APP \
          -e DEBUG=true \
          -e DJANGO_SETTINGS_MODULE=coda.settings.test \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          -v /home/<USER>/jenkins_home/workspace/codapipeline/test-results:/app/src/test-results \
          --entrypoint /bin/bash \
          "$ECR_BASE_URL/test:$TAG" -c "sudo chmod 777 /app/src/test-results && flake8 --exit-zero . > flake8_output.txt && flake8_junit flake8_output.txt /app/src/test-results/flake8_results.xml"
        ;;
      testbash)
        APP_LOG_FOLDER="$LOG_FOLDER/$APP"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP_$TAG" || true
        docker rm "$APP_$TAG" || true
        docker run --pull=always $DOCKER_RUN_MODE \
          --name "$APP_$TAG" \
          --rm \
          -e WORKER_TYPE=APP \
          -e DEBUG=true \
          -e DJANGO_SETTINGS_MODULE=coda.settings.test \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          --entrypoint /bin/bash \
          "$ECR_BASE_URL/test:$TAG" -c "bash"
        ;;

      # Nginx reverse proxy
      nginx)
        docker stop "$APP" || true
        docker rm "$APP" || true
        docker run --pull=always $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          $NGINX_PORT_FORWARDING \
          -v $LOG_FOLDER/nginx:/var/log/nginx \
          -v $USER_HOME/static:/usr/share/nginx/static \
          "$ECR_BASE_URL/nginx:$TAG"
        ;;
      nginxbash)
        docker run --pull=always $DOCKER_RUN_MODE \
          --name "$APP" \
          --rm \
          $NGINX_PORT_FORWARDING \
          -v $LOG_FOLDER/nginx:/var/log/nginx \
          -v $USER_HOME/static:/usr/share/nginx/static \
          "$ECR_BASE_URL/nginx:$TAG" bash
        ;;

      # Transcription app
      transcription)
        APP_LOG_FOLDER="$LOG_FOLDER/gpunode"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        sleep 5
        docker run --pull=always $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -e CT2_CUDA_ALLOW_FP16=1 \
          -e WORKER_TYPE=TRANSCRIPTION \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          $COMMON_GPU_SETTINGS \
          "$ECR_BASE_URL/transcription:$TAG" "$APP"
        ;;

      # Summary/Red Flag app
      summaryredflag|summary|redflag)
        APP_LOG_FOLDER="$LOG_FOLDER/gpunode"
        setup_log_folder "$APP_LOG_FOLDER"
        docker stop "$APP" || true
        docker rm "$APP" || true
        sleep 5
        docker run --pull=always $DOCKER_RUN_MODE \
          --name "$APP" \
          --restart unless-stopped \
          -e WORKER_TYPE=GPU24 \
          $COMMON_GPU_SETTINGS \
          -v $APP_LOG_FOLDER:/home/<USER>/coda_logs \
          "$ECR_BASE_URL/llm_generic:$TAG" "$APP"
        ;;

      *)
        echo "Invalid APP value: $APP"
        echo "Valid options: admin, messenger, defaultworker, summaryapi, redflagapi, summaryredflagapi, flower, transcription, summaryredflag, bash"
        exit 1
        ;;
    esac
  done
}


function stop_help {
  echo -e "\n\033[1mcoda stop\033[0m - Stop Docker containers for specified applications."
  echo
  echo -e "\033[1mUsage:\033[0m"
  echo "  ./coda stop [APP] [OPTIONS]"
  echo
  echo -e "\033[1mArguments:\033[0m"
  echo -e "  \033[1mAPP\033[0m:"
  echo "      The application name for which to stop containers."
  echo "      If omitted, stops containers for all known applications."
  echo
  echo -e "\033[1mOptions:\033[0m"
  echo "  --force, -f                    Force stop containers (docker rm -f)."
  echo "  --remove                       Remove containers after stopping them."
  echo "                                 Default: false"
  echo "  --help, -h                     Display this help message."
  echo
  echo -e "\033[1mExamples:\033[0m"
  echo "  Stop all containers for all applications:"
  echo "    ./coda stop"
  echo "  Stop containers for the admin application:"
  echo "    ./coda stop admin"
  echo "  Force stop and remove containers for the transcription application:"
  echo "    ./coda stop transcription --force --remove"
  exit 0
}

# Helper function to stop containers by app name
function stop_coda_containers {
  local app_name="$1"
  local coda_container=$(docker ps --filter "name=$app_name" -q)

  if [ -z "$coda_container" ]; then
    echo "No containers running for: $app_name"
    # try to remove it anyway in case it exists from previous builds
    docker rm $app_name || true
    return
  fi

  if [ "$FORCE" = true ]; then
    echo "Force stopping containers for: $app_name"
    docker exec $coda_container kill -SIGTERM 1 || true
    sleep 2
    docker exec $coda_container sh -c "kill -9 -1" || true
    sleep 2
    docker exec $coda_container kill -SIGKILL 1 || true
    sleep 2
    docker rm -f $coda_container || true
  else
    echo "Stopping containers for: $app_name"
    docker stop $coda_container
  fi

  if [ $? -eq 0 ]; then
    echo "Successfully stopped (forced=$FORCE) containers for application: $app_name"

    # Wait for containers to fully stop
    echo "Waiting for containers to completely stop..."
    local max_attempts=200
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
      local running_container=$(docker ps --filter "name=$app_name" -q)
      if [ -z "$running_container" ]; then
        echo "All containers for $app_name have stopped."
        break
      fi

      echo "Waiting for containers to stop... Attempt $attempt/$max_attempts"
      sleep 2
      attempt=$((attempt + 1))
    done

    # Check if we timed out
    local still_running=$(docker ps --filter "name=$app_name" -q)
    if [ -n "$still_running" ]; then
      echo "WARNING: Some containers for $app_name are still running after waiting"

      # Try more forceful approach on stubborn containers
      for container in $still_running; do
        echo "Attempting to forcefully terminate container $container"
        docker exec $container kill -SIGKILL 1 || true
        docker rm -f $container || true
        sleep 3
      done
    fi
  else
    echo "Failed to stop containers for application: $app_name"
    exit 1
  fi

  # Remove containers if requested
  if [ "$REMOVE" = true ] && [ "$FORCE" != true ]; then
    echo "Removing containers for: $app_name"
    docker rm $coda_container || echo "Failed to remove some containers for $app_name"
  fi
}

function coda_stop {
  if [[ "$1" == "--help" || "$1" == "-h" ]]; then
      stop_help
      exit 0
  fi

  # List of known applications to be stopped
  APPS=("messenger" "defaultworker" "summaryapi" "redflagapi" "summaryredflagapi" "flower" "bash" "migrate" "getpendingmigrations" "admindev" "admin" "nginx" "transcription" "summaryredflag" "summary" "redflag")

  # If the first argument is missing or not recognized,
  # treat it as "all"
  APP=${1:-all}
  FORCE=false
  REMOVE=false

  # Parse arguments
  shift 2>/dev/null || true  # Shift past APP if it exists
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --force|-f)
        FORCE=true
        ;;
      --remove)
        REMOVE=true
        ;;
      *)
        echo "Unknown option: $1"
        exit 1
        ;;
    esac
    shift
  done


  # Stop logic
  if [ "$APP" == "all" ]; then
    for app in "${APPS[@]}"; do
      stop_coda_containers "$app"
    done
  else
    stop_coda_containers "$APP"
  fi
}


function build_help {
  echo -e "\n\033[1mcoda_build\033[0m - A script to build Docker images for the specified applications."
  echo
  echo -e "\033[1mRequirements:\033[0m"
  echo "  - awscli: Required to fetch the Docker ECR login credentials."
  echo "  - Docker: Required to pull and run Docker containers."
  echo
  echo -e "\033[1mUsage:\033[0m"
  echo "  ./coda_build [OPTIONS] [TAG] [SPECIFIED_IMAGE]"
  echo
  echo -e "\033[1mArguments:\033[0m"
  echo -e "  \033[1mTAG\033[0m:"
  echo "      The Docker image tag to use (e.g., 'latest', 'stable', etc.)."
  echo -e "  \033[1mSPECIFIED_IMAGE\033[0m:"
  echo "      The specific image to build. Use 'all' to build all images."
  echo
  echo -e "\033[1mOptions:\033[0m"
  echo "  -y                                Auto-confirm prompts."
  echo "  --force-rebuild                   Force a rebuild without using the cache."
  echo "  --verbose                         Enable verbose output."
  echo "  --aws-region <REGION>             Specify the AWS region. Optional. Defaults to: '$REGION'."
  echo "  --aws-account-id <ACCOUNT_ID>     Specify the AWS account ID. Optional. Defaults to: '$ACCOUNT_ID'."
  echo "  --ecr-base_repo <BASE_REPO>       Specify the base repository. Optional. Defaults to: '$BASE_REPO'."
  echo "  --uid <USER_UID>                  Override the default UID (User ID). Defaults to the current user's UID or SUDO_UID when running with sudo."
  echo "  --gid <USER_GID>                  Override the default GID (Group ID). Defaults to the current user's GID or SUDO_GID when running with sudo."
  echo "  --no-migration-check              Disable the migration check."
  echo "  --no-trivy-scan                   Skip the Trivy vulnerability scan."
  echo "  --daemon, -d                      Run the container in detached mode."
  echo "  --help, -h                        Display this help message."
  echo
  echo -e "\033[1mExamples:\033[0m"
  echo "  Build the admin application with the 'stable' tag:"
  echo "    ./coda_build stable admin"
  echo "  Build all images with verbose output and force rebuild:"
  echo "    ./coda_build --verbose --force-rebuild all"
  echo "  Build without performing a Trivy scan:"
  echo "    ./coda_build --no-trivy-scan stable admin"
  echo "  Override UID and GID while skipping Trivy scan:"
  echo "    ./coda_build --no-trivy-scan --uid 1001 --gid 1001 stable admin"
}

ensure_buildx_builder_exists() {
  local BUILDER_NAME="codabuilder"

  # Check if the builder already exists
  if ! docker buildx inspect "$BUILDER_NAME" > /dev/null 2>&1; then
    echo "Buildx builder '$BUILDER_NAME' does not exist. Creating..."

    # Create the builder
    docker buildx create --name "$BUILDER_NAME" --use

    # Verify creation
    if docker buildx inspect "$BUILDER_NAME" > /dev/null 2>&1; then
      echo "Buildx builder '$BUILDER_NAME' created and is now active."
    else
      echo "Failed to create Buildx builder '$BUILDER_NAME'. Please check Docker Buildx installation."
      exit 1
    fi
  else
    echo "Buildx builder '$BUILDER_NAME' already exists."

    # Set the builder as the current builder
    docker buildx use "$BUILDER_NAME"
  fi
}


function coda_build {
  if [[ "$1" == "--help" || "$1" == "-h" ]]; then
      build_help
      exit 0
  fi

  # Default settings (adjust to match your build environment):
  REGION="us-east-1"
  ACCOUNT_ID="************"
  BASE_REPO="symphony-staging/coda"
  USER_UID=${SUDO_UID:-$(id -u)}
  USER_GID=${SUDO_GID:-$(id -g)}
  AUTO_CONFIRM=false
  FORCE_REBUILD=false
  VERBOSE=false
  TRIVY_SCAN=true

  # Parse options until we reach non-option arguments
  while [[ "$1" =~ ^- ]]; do
    case $1 in
      -y) AUTO_CONFIRM=true ;;
      --force-rebuild) FORCE_REBUILD=true ;;
      --verbose) VERBOSE=true ;;
      --aws-region) shift; REGION=$1 ;;
      --aws-account-id) shift; ACCOUNT_ID=$1 ;;
      --ecr-base_repo) shift; BASE_REPO=$1 ;;
      --uid) shift; USER_UID=$1 ;;
      --gid) shift; USER_GID=$1 ;;
      --no-trivy-scan) TRIVY_SCAN=false ;;
      *) echo "Error: Unknown argument $1"; exit 1 ;;
    esac
    shift
  done

  # Remaining arguments: TAG and SPECIFIED_IMAGE
  TAG=${1:-latest}
  SPECIFIED_IMAGE=${2:-all}

  BASE_REGISTRY_PATH="$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$BASE_REPO"

  # Example images list
  IMAGES=("app" "transcription" "llm_generic")
  SELECTED_IMAGES=("${IMAGES[@]}")

  if [ "$SPECIFIED_IMAGE" != "all" ]; then
    SELECTED_IMAGES=("$SPECIFIED_IMAGE")
  fi

  echo "Building Docker images with the following details:"
  echo " - ECR path: $BASE_REGISTRY_PATH"
  echo " - Tag: $TAG"
  echo " - UID: $USER_UID"
  echo " - GID: $USER_GID"
  echo " - Images: ${SELECTED_IMAGES[*]}"
  echo " - Force Rebuild: $FORCE_REBUILD"
  echo " - Verbose: $VERBOSE"
  echo

  if [ "$AUTO_CONFIRM" = false ]; then
    read -rp "Do you want to proceed with the build? (y/N): " CONFIRM
    [[ "$CONFIRM" != [Yy]* ]] && echo "Build canceled." && exit 0
  fi

  # -------------------- Trivy Scan Step Using Docker --------------------
  if [ "$TRIVY_SCAN" = true ]; then
    echo "Starting Trivy scan to detect vulnerabilities..."

    # Define the project directory (adjust if needed)
    PROJECT_DIR="$(pwd)"

    # Run Trivy using Docker
    docker run --rm -v "$PROJECT_DIR":/project aquasec/trivy:latest fs /project \
      --exit-code 1 \
      --severity HIGH,CRITICAL \
      --no-progress

    TRIVY_EXIT_CODE=$?

    if [ $TRIVY_EXIT_CODE -ne 0 ]; then
      echo "Trivy scan detected vulnerabilities. Please address them before building."
      exit 1
    fi

    echo "Trivy scan passed. Proceeding with the build..."
  else
    echo "Trivy scan skipped as per user request."
  fi
  # ----------------------------------------------------------------------


  # Example: Docker login, building, pushing, etc. (adjust as needed)
  echo "Logging in to registry..."
  aws ecr get-login-password --region "$REGION" | docker login \
    --username AWS --password-stdin "$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com"

  ensure_buildx_builder_exists

  # Helpers for Docker build command options
  VERBOSE_FLAG=$([ "$VERBOSE" = true ] && echo "--progress=plain")
  REBUILD_FLAG=$([ "$FORCE_REBUILD" = true ] && echo "--no-cache")

  # Function to ensure ECR repository exists
  ensure_repository_exists() {
    local REPOSITORY_NAME="$1"

    # Check if the repository exists
    if ! aws ecr describe-repositories --region "$REGION" --repository-names "$REPOSITORY_NAME" > /dev/null 2>&1; then
      echo "Repository $REPOSITORY_NAME does not exist. Creating..."
      aws ecr create-repository --region "$REGION" --repository-name "$REPOSITORY_NAME" >/dev/null
      echo "Repository $REPOSITORY_NAME created successfully."
    else
      echo "Repository $REPOSITORY_NAME already exists."
    fi
  }

  # Build and push images
  for IMAGE in "${SELECTED_IMAGES[@]}"; do
    IMAGE_PATH="$BASE_REGISTRY_PATH/${IMAGE}"
    echo "Ensuring repository exists for $IMAGE"

    # Extract repository name from the image path
    REPOSITORY_NAME="symphony-staging/coda/${IMAGE}"
    ensure_repository_exists "$REPOSITORY_NAME"

    echo "Building and pushing image: $IMAGE with tag: $TAG"
    docker buildx build \
      $REBUILD_FLAG \
      $VERBOSE_FLAG \
      --build-arg UID="$USER_UID" \
      --build-arg GID="$USER_GID" \
      --target "$IMAGE" \
      --cache-to type=registry,mode=max,image-manifest=true,oci-mediatypes=true,ref="${IMAGE_PATH}:cache" \
      --cache-from type=registry,ref="${IMAGE_PATH}:cache" \
      -t "${IMAGE_PATH}:${TAG}" \
      $([ "$TAG" != "latest" ] && echo "-t ${IMAGE_PATH}:latest") \
      --push .
  done
}


function show_coda_help {
  echo "coda.sh - A versatile script for managing Docker-based tasks."
  echo ""
  echo "Usage:"
  echo "  ./coda.sh <subcommand> [arguments...]"
  echo ""
  echo "Subcommands:"
  echo "  run       Run coda application."
  echo "  stop      Stop coda application."
  echo "  cleanup   Cleanup unused Docker resources (containers, images, volumes, networks, and Buildx)."
  echo "  build     Build coda application."
  echo "  test      Run unittest for coda application."
  echo "  help      Display this help message."
  echo ""
  echo "Use './coda.sh <subcommand> --help' for more information on a specific subcommand."
  exit 0
}

# Help: Cleanup subcommand usage instructions
function cleanup_help {
  echo "Docker and Buildx Cleanup Tool"
  echo ""
  echo "Usage:"
  echo "  ./coda.sh cleanup [options]"
  echo ""
  echo "Options:"
  echo "  --all              Clean all resources (default if no options are given)"
  echo "  --containers       Clean up stopped containers"
  echo "  --images           Clean up dangling (unused) images"
  echo "  --volumes          Clean up unused volumes"
  echo "  --networks         Clean up unused Docker networks"
  echo "  --buildx           Clean up Buildx builders and build cache"
  echo "  --help, -h         Show this help message and exit"
  echo ""
  echo "Examples:"
  echo "  ./coda.sh cleanup --all              # Cleans all resources"
  echo "  ./coda.sh cleanup --volumes          # Only clean unused volumes"
  echo "  ./coda.sh cleanup --images --buildx  # Clean dangling images and Buildx resources"
  exit 0
}

# Docker cleanup functions
function cleanup_containers {
  echo "Removing stopped containers..."
  docker container prune -f
}

function cleanup_images {
  echo "Removing dangling images..."
  # Define the patterns to search for
  PATTERNS=("coda/transcription" "coda/app" "coda/nginx" "coda/llm_generic")

  # Loop through each pattern
  for pattern in "${PATTERNS[@]}"; do
    echo "Cleaning up old $pattern images..."
    docker images \
      | grep "$pattern" \
      | awk '{print $2, $3}' \
      | sort -r \
      | tail -n +4 \
      | awk '{print $2}' \
      | xargs -r docker rmi -f
  done

  docker image prune -f
}

function cleanup_volumes {
  echo "Removing unused volumes..."
  docker volume prune -f
}

function cleanup_networks {
  echo "Removing unused networks..."
  docker network prune -f
}

function cleanup_buildx {
  echo "Cleaning up Buildx resources..."

  # Ensure Buildx is installed
  if ! docker buildx version > /dev/null 2>&1; then
    echo "Docker Buildx plugin is not installed. Skipping Buildx cleanup..."
    return
  fi

  # Clean up inactive/old Buildx builders
  echo "Cleaning up unused Buildx builders..."
  docker buildx rm $(docker buildx ls) 2>/dev/null || echo "No inactive builders to remove."

  # Clean Buildx cache
  echo "Cleaning up Buildx build cache..."
  docker buildx prune -f
}

# Main Cleanup Subcommand
function coda_cleanup {
  # If help flag is passed, display cleanup help
  if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    cleanup_help
    exit 0
  fi

  # Parse arguments
  cleanup_all=false
  cleanup_specific=false

  # If no arguments are given, default to cleaning up all resources
  if [[ $# -eq 0 ]]; then
    cleanup_all=true
  fi

  while [[ "$1" ]]; do
    case $1 in
      --all)
        cleanup_all=true
        ;;
      --containers)
        cleanup_containers_flag=true
        cleanup_specific=true
        ;;
      --images)
        cleanup_images_flag=true
        cleanup_specific=true
        ;;
      --volumes)
        cleanup_volumes_flag=true
        cleanup_specific=true
        ;;
      --networks)
        cleanup_networks_flag=true
        cleanup_specific=true
        ;;
      --buildx)
        cleanup_buildx_flag=true
        cleanup_specific=true
        ;;
      *)
        echo "Unknown option: $1"
        cleanup_help
        ;;
    esac
    shift
  done

  echo "=== Docker and Buildx Cleanup ==="

  # Execute the appropriate cleanup functions
  if [ "$cleanup_all" = true ]; then
    echo "Cleaning all resources (containers, images, volumes, networks, and Buildx)..."
    cleanup_containers
    cleanup_images
    cleanup_volumes
    cleanup_networks
    cleanup_buildx
  else
    [ "$cleanup_containers_flag" = true ] && cleanup_containers
    [ "$cleanup_images_flag" = true ] && cleanup_images
    [ "$cleanup_volumes_flag" = true ] && cleanup_volumes
    [ "$cleanup_networks_flag" = true ] && cleanup_networks
    [ "$cleanup_buildx_flag" = true ] && cleanup_buildx
  fi

  echo "Cleanup process completed!"
}

function coda_test {
  coda_build -y --no-trivy-scan --uid 1004 --gid 1004 unittest test "$@"
  coda_run unittest test --uid 1004 --gid 1004 --no-migration-check "$@"
}

coda_wait_for_init() {
    local CONTAINER_NAME=""
    local SEARCH_PATTERN="Started worker"
    local MAX_WAIT_TIME=600
    local CONTAINER_WAIT_TIME=120
    local WAIT_INTERVAL=10

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --search-pattern)
                SEARCH_PATTERN="$2"
                shift 2
                ;;
            --max-wait-time)
                MAX_WAIT_TIME="$2"
                shift 2
                ;;
            --help|-h)
                echo "Usage: $0 wait-for-init CONTAINER_NAME [OPTIONS]"
                echo "  CONTAINER_NAME              Name of the container to check"
                echo "Options:"
                echo "  --search-pattern TEXT       Text to search for in logs (default: 'Started worker')"
                echo "  --max-wait-time SECONDS     Maximum time to wait in seconds (default: 600)"
                echo "  --help, -h                  Display this help message"
                exit 0
                ;;
            *)
                if [[ -z "$CONTAINER_NAME" ]]; then
                    CONTAINER_NAME="$1"
                else
                    echo "Error: Unexpected argument '$1'"
                    echo "Use --help for usage information"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Check if required parameters are provided
    if [[ -z "$CONTAINER_NAME" ]]; then
        echo "Error: Container name is required"
        echo "Usage: $0 wait-for-init CONTAINER_NAME [OPTIONS]"
        echo "Use --help for more information"
        exit 1
    fi

    # Rest of your function's implementation goes here
    echo "Waiting for container $CONTAINER_NAME to initialize..."
    echo "Search pattern: $SEARCH_PATTERN"
    echo "Maximum wait time: $MAX_WAIT_TIME seconds"

    # Start time for container existence check
    container_start_time=$(date +%s)

    echo "Checking if container ${CONTAINER_NAME} exists..."

    # First loop: Wait for container to exist (with 2-minute timeout)
    while true; do
        # Check if container exists
        if docker ps -q -f name="${CONTAINER_NAME}" | grep -q .; then
            echo "Container ${CONTAINER_NAME} found. Now waiting for initialization..."
            break
        fi

        # Check if timeout for container existence has been reached
        current_time=$(date +%s)
        elapsed_time=$((current_time - container_start_time))

        if [ ${elapsed_time} -ge ${CONTAINER_WAIT_TIME} ]; then
            echo "Timeout reached waiting for container ${CONTAINER_NAME} to exist."
            echo "Container might not have started properly."
            exit 1
        fi

        echo "Waiting for container ${CONTAINER_NAME} to start... (${elapsed_time}s elapsed)"
        sleep ${WAIT_INTERVAL}
    done

    # Start time for initialization check
    start_time=$(date +%s)

    echo "Waiting for container ${CONTAINER_NAME} initialization message..."

    # Second loop: Wait for initialization message in logs
    while true; do
        # Check if the message appears in logs
        if docker logs "${CONTAINER_NAME}" 2>&1 | grep -q "${SEARCH_PATTERN}"; then
            echo "Container ${CONTAINER_NAME} successfully initialized!"
            exit 0
        fi

        # Check if timeout has been reached
        current_time=$(date +%s)
        elapsed_time=$((current_time - start_time))

        if [ ${elapsed_time} -ge ${MAX_WAIT_TIME} ]; then
            echo "Timeout reached waiting for container ${CONTAINER_NAME} initialization."
            echo "Container may still be initializing, but continuing anyway. MANUALLY CHECK CONTAINER STATUS"
            exit 0
        fi

        echo "Waiting for container ${CONTAINER_NAME} to initialize... (${elapsed_time}s elapsed)"
        sleep ${WAIT_INTERVAL}
    done
}



# Main Script Execution
if [[ $# -eq 0 ]]; then
  show_coda_help
fi

# Parse the subcommand
subcommand=$1
shift


# The main dispatcher for subcommands
case $subcommand in
  run)
    coda_run "$@"
    ;;
  stop)
    coda_stop "$@"
    ;;
  build)
    coda_build "$@"
    ;;
  cleanup)
    coda_cleanup "$@"
    ;;
  test)
    coda_test "$@"
    ;;
  wait-for-init)
    coda_wait_for_init "$@"
    ;;
  help | --help | -h)
    show_coda_help
    ;;
  *)
    echo "Unknown subcommand: $1"
    echo "Use '$0 run --help' for usage on the 'run' subcommand."
    exit 1
    ;;
esac
