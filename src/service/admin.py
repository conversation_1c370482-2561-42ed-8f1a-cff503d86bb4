from django.contrib import admin, messages
from django.db.models import J<PERSON><PERSON>ield
from django.urls import reverse
from django.utils.html import format_html
from django_json_widget.widgets import JSONEditorWidget

from service.models import ServiceConfiguration, CODAService, UserProfile


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ["get_username", "use_tenant_id"]

    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"
        app_label = "auth"

    def get_username(self, obj):
        return obj.user.username

    get_username.short_description = "Username"


@admin.action(description="Duplicate instance")
def duplicate(modeladmin, request, queryset):
    if len(queryset) == 1:
        new_instance = queryset.first()
        new_instance.id = None
        new_instance.name = f"{queryset.first().name} (COPY)"
        new_instance.active = False
        new_instance.save()
    else:
        messages.add_message(request, messages.ERROR, "Only single instance selection is allowed")


@admin.register(ServiceConfiguration)
class ServiceConfigurationAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "service_settings",
        # "llm_model_link",
        # "get_transcription_settings",
        # "get_summary_settings",
        # "get_redflag_settings",
    ]
    # readonly_fields = ("default",)
    actions = [
        duplicate,
    ]

    search_fields = ("name",)
    # list_filter = ("llm_model",)
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }

    # def reuse_transcription(self, obj):
    #     return obj.transcription_settings.reuse_existing
    #
    # def get_transcription_settings(self, obj):
    #     """
    #     Dynamically build and pretty-print the transcription settings for admin display.
    #     """
    #     settings = obj.transcription_settings
    #     settings_data = {key: value for key, value in settings.__dict__.items()}
    #     pretty_json = json.dumps(settings_data, indent=4)
    #     return mark_safe(f"<pre>{pretty_json}</pre>")
    #
    # def get_summary_settings(self, obj):
    #     """
    #     Dynamically build the summary settings for display in the admin.
    #     """
    #     settings = obj.summary_settings
    #     settings_data = {key: value for key, value in settings.__dict__.items()}
    #     pretty_json = json.dumps(settings_data, indent=4)
    #     return mark_safe(f"<pre>{pretty_json}</pre>")
    #
    # def get_redflag_settings(self, obj):
    #     """
    #     Dynamically build the redflag settings for display in the admin.
    #     """
    #     settings = obj.red_flag_settings
    #     settings_data = {key: value for key, value in settings.__dict__.items()}
    #     pretty_json = json.dumps(settings_data, indent=4)
    #     return mark_safe(f"<pre>{pretty_json}</pre>")
    #
    #
    # get_transcription_settings.short_description = "Transcription Settings"
    # get_summary_settings.short_description = "Summary Settings"
    # get_redflag_settings.short_description = "Red-flag Settings"
    #
    # def llm_model_link(self, obj):
    #     url = reverse("admin:llm_model_llmmodel_change", args=[obj.llm_model.id])
    #     return format_html('<a href="{}">{}</a>', url, obj.llm_model.model_name)
    #
    # llm_model_link.short_description = "LLM Model"
    #
    # reuse_transcription.short_description = "Reuse Transcription"


@admin.register(CODAService)
class CODAServiceAdmin(admin.ModelAdmin):
    """Admin interface for CODAService."""

    list_display = (
        "id",
        "name",
        "configuration_link",
        "enabled",
    )

    ordering = ["-created_at"]
    search_fields = ["name"]
    fields = ["name", "description", "configuration", "enabled"]
    readonly_fields = ["created_at", "updated_at"]

    def configuration_link(self, obj):
        url = reverse("admin:service_serviceconfiguration_change", args=[obj.configuration.id])
        return format_html('<a href="{}">{}</a>', url, obj.configuration.name)

    configuration_link.short_description = "Service Configuration"
