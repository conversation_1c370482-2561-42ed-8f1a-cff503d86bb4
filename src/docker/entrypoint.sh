#!/bin/bash
set -e

echo "Entrypoint: Starting container..."

# Check if any arguments were passed to the container
if [ $# -gt 0 ]; then
  case "$1" in
    messenger)
      echo "Option selected: messenger service"
      shift
      exec python3 manage.py messenger
      ;;
    admindev)
      echo "Option selected: admin service"
      shift
      exec python3 manage.py runserver 0.0.0.0:8000
      ;;
    admin)
      echo "Option selected: admin service"
      shift
      python3 manage.py collectstatic --no-input
      mkdir -p /home/<USER>/coda_logs/gunicorn
      chmod -R 755 /home/<USER>/coda_logs/gunicorn
      exec gunicorn coda.wsgi:application -c docker/gunicorn_config.py
      ;;
    migrate)
      echo "Option selected: migrate"
      shift
      exec python3 manage.py migrate
      ;;
    getpendingmigrations)
      echo "Option selected: getpendingmigrations"
      shift
      exec python3 manage.py showmigrations --plan
      ;;
    flower)
      echo "Option selected: flower service"
      shift
      exec celery -A coda.celery flower -l INFO --port=8001 "$@"
      ;;
    defaultworker)
      echo "Option selected: defaultworker service"
      shift
      exec celery -A coda.celery worker -l INFO "$@"
      ;;
    transcription)
      echo "Option selected: Transcription service"
      shift
      exec celery -A coda.celery worker -l INFO --concurrency 1 -Q transcription "$@"
      ;;
    summary)
      echo "Option selected: LLM summary service"
      shift
      exec celery -A coda.celery worker -l INFO --concurrency 1 -Q summary "$@"
      ;;
    redflag)
      echo "Option selected: LLM redflag service"
      shift
      exec celery -A coda.celery worker -l INFO --concurrency 1 -Q redflag "$@"
      ;;
    summaryredflag)
      echo "Option selected: LLM summary and redflag service"
      shift
      exec celery -A coda.celery worker -l INFO --concurrency 1 -Q summary,redflag "$@"
      ;;
    *)
      # If the provided command does not match either option, execute it as given.
      exec "$@"
      ;;
  esac
fi

# If no command-line arguments, then check for the SERVICE environment variable.
if [ -n "$WORKER_TYPE" ]; then
  case "$WORKER_TYPE" in
    TRANSCRIPTION)
      echo "WORKER_TYPE variable set to transcription; starting transcription service..."
      exec celery -A coda.celery worker -l DEBUG --concurrency 1 -Q transcription
      ;;
    GPU24)
      echo "SERVICE variable set to summary; starting summary service..."
      exec celery -A coda.celery worker -l INFO --concurrency 1 -Q summary,redflag
      ;;
    *)
      echo "Unknown SERVICE value: $SERVICE"
      exit 1
      ;;
  esac
fi

# If neither command-line arguments nor SERVICE variable are provided,
# then execute the default CMD from the image.
echo "No service option provided; executing default CMD."
exec "$@"
