import logging
import socket
from functools import lru_cache

from billiard.exceptions import SoftTimeLimitExceeded
from celery import Task, signals
from django.conf import settings
from django.db import transaction
from django.utils import timezone

from common.rabbitmq import IndexPublisher
from common.tools import GPUMemoryDetails, update_nested_dict
from llm_model.models import TaskStateModel
from tenant.models import ParleyTranscription
from worker.models import WorkerHost, WorkflowJob, ServiceJob, ServiceJobStatusChoices, ServiceJobType
import subprocess
from ec2_metadata import ec2_metadata


logger = logging.getLogger(__name__)

try:
    from transcription.services import TranscriptionModelService
    from llm_model.services.llm_services.llm_local import LLMModelService
    from llm_model.services.llm_services.llm_remote import LLMRemoteModelService
    from llm_model.exceptions import BaseGPUServiceException, NoRemoteAPIModelServiceException
except Exception as e:
    logger.debug(str(e))


def get_ec2_instance_type_old():
    commands = ["ec2metadata --instance-type", "ec2metadata --instance-type --net=host"]
    for command in commands:
        result = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE).stdout.read().strip().decode("utf-8")
        if result != "":
            return result

    return "unknown"


@lru_cache(maxsize=10)
def get_ec2_instance_type():
    try:
        return ec2_metadata.instance_type
    except Exception:
        return "unknown"


class BaseTaskWithRetry(Task):
    """
    Base celery task that uses exponential retry backoff
    """

    WORKER_TYPE = "APP"
    autoretry_for = (Exception,)
    retry_kwargs = {"max_retries": 2}
    retry_backoff = 2
    time_limit = 60 * 8  # 8 minutes time limit

    def __init__(self):
        super().__init__()
        logger.debug(f"Initializing {self.WORKER_TYPE} worker task {self.name}")
        self.task_worker = None
        self.task_service = None
        self.hostname = socket.gethostname()
        if settings.LOCAL_TESTING:
            self.ec2_type = "local"
        else:
            self.ec2_type = get_ec2_instance_type()
        self.worker_name = f"{self.ec2_type}--{self.hostname}"
        if self.is_worker_type():
            signals.worker_process_init.connect(self.on_worker_process_init)
            signals.worker_process_shutdown.connect(self.on_worker_process_shutdown)

    def is_worker_type(self):
        return settings.WORKER_TYPE == self.WORKER_TYPE

    def on_worker_process_shutdown(self, *args, **kwargs):
        gpu_worker = WorkerHost.objects.filter(name=self.worker_name).first()
        gpu_worker.stop()
        logger.info(f"Stopped worker {self.WORKER_TYPE} on {self.task_worker.hostname}")

    def on_worker_process_init(self, *args, **kwargs):
        logger.info("Starting base worker")
        logger.debug(f"BaseTaskWithRetry on_worker_process_init START {self.name}")

        self.task_worker, created = WorkerHost.objects.get_or_create(name=self.worker_name)
        if created:
            logger.info(f"Created new worker on {self.hostname} - {self.ec2_type} with default configuration")
            self.task_worker.hostname = self.hostname
            self.task_worker.description = f"{self.WORKER_TYPE} worker"
            self.task_worker.ec2_type = self.ec2_type
            self.task_worker.save()
        else:
            logger.info(f"Found existing {self.WORKER_TYPE} worker {self.worker_name}")

        self._app.task_worker = self.task_worker
        self.task_worker.start()
        logger.info(f"Started worker {self.WORKER_TYPE} on {self.task_worker.hostname}")

    def get_workflow_metadata(self, *args, **kwargs):
        workflow_metadata = kwargs.get("workflow_metadata", {})
        if workflow_metadata == {}:
            logger.error(f"Task {self.id} Failed. Missing/Invalid workflow_metadata kwarg")
            raise ValueError(f"Task {self.id} Failed. Missing/Invalid workflow_metadata kwarg")
        required_metadata = ["workflow_job_id", "parley_id", "customer_id", "language"]
        if any(value is None for value in [workflow_metadata.get(key) for key in required_metadata]):
            raise ValueError("Missing parley_id, customer_id or language kwarg")

        return workflow_metadata


class BaseGPUTaskWithRetry(BaseTaskWithRetry):
    """
    Abstract class. DO NOT INSTANTIATE
    """

    WORKER_TYPE = "Not Implemented"
    GPU_SERVICE = None

    def __init__(self):
        if self.is_worker_type():
            super().__init__()
            self.task_gpu_worker = None
            self.task_service = None

    def on_worker_process_shutdown(self, *args, **kwargs):
        gpu_worker = WorkerHost.objects.filter(name=self.worker_name).first()
        gpu_worker.stop()
        logger.info(f"Stopped worker {self.WORKER_TYPE} on {self.task_gpu_worker.hostname}")

    def on_worker_process_init(self, *args, **kwargs):
        logger.info(f"Starting worker of type {self.WORKER_TYPE}...")
        logger.debug(f"BaseGPUTaskWithRetry on_worker_process_init START {self.name} - {self._app}")

        self.task_gpu_worker, created = WorkerHost.objects.get_or_create(name=self.worker_name)
        llm_model = None
        if created:
            logger.info(
                f"Created new {self.WORKER_TYPE} worker on {self.hostname} - {self.ec2_type} with default configuration"
            )
            self.task_gpu_worker.hostname = self.hostname
            self.task_gpu_worker.description = f"{self.WORKER_TYPE} worker"
            self.task_gpu_worker.ec2_type = self.ec2_type
            self.task_gpu_worker.save()
        else:
            logger.info(f"Found existing {self.WORKER_TYPE} worker {self.worker_name}")
            if self.task_gpu_worker.llm_model is not None:
                llm_model = self.task_gpu_worker.llm_model
                logger.info(
                    f"Worker {self.worker_name} is initialising with model {llm_model.model_name}"
                )
        self.task_gpu_worker.loading()
        self._app.gpu_worker = self.task_gpu_worker
        try:
            logger.info(f"Loading service with model {llm_model}")
            self.task_service = self.GPU_SERVICE(llm_model)
            self._app.service = self.task_service
        except Exception as e:
            self.task_service = None
            logger.exception(f"Unable to initialize service {self.WORKER_TYPE} - {str(e)}")
        self.task_gpu_worker.start()
        logger.info(f"Started worker {self.WORKER_TYPE} on {self.task_gpu_worker.hostname}")

    @property
    def gpu_worker(self):
        return self.task_gpu_worker

    @property
    def service(self):
        return self.task_service


class BaseLLMTaskWithRetry(BaseGPUTaskWithRetry):
    WORKER_TYPE = "GPU24"
    try:
        GPU_SERVICE = LLMModelService
    except Exception:
        pass


class BaseLLMRemoteTaskWithRetry(BaseGPUTaskWithRetry):
    WORKER_TYPE = "APP"
    try:
        GPU_SERVICE = LLMRemoteModelService
    except Exception:
        pass


class BaseTranscriptionTaskWithRetry(BaseGPUTaskWithRetry):
    WORKER_TYPE = "TRANSCRIPTION"
    try:
        GPU_SERVICE = TranscriptionModelService
    except Exception:
        pass


class BaseTask:
    def __init__(self, *args, **kwargs):
        self.task = kwargs.get("task")
        if self.task is None:
            raise ValueError("Missing task kwarg")
        workflow_metadata = self.task.get_workflow_metadata(*args, **kwargs)
        self.indexing_event_type = kwargs.get("indexing_event_type")
        self.index_publish_enabled = workflow_metadata.get("index_publish_enabled", False)
        self.service_metadata = workflow_metadata.get("service_metadata", {})
        self.parley_id = workflow_metadata.get("parley_id")
        self.customer_id = workflow_metadata.get("customer_id")
        self.language = workflow_metadata.get("language")
        self.workflow_job = WorkflowJob.objects.get(id=workflow_metadata["workflow_job_id"])

        self.job = ServiceJob(
            name=self.task.name,
            parley_id=self.parley_id,
            customer_id=self.customer_id,
            language=self.language,
            worker=None,
            service_configuration=None,
            type=ServiceJobType.OTHER,
            workflow=self.workflow_job,
        )
        logger.info(f"Created job {self.job_details}]")

    def run(self):
        logger.info(f"{self.job_details} starting")
        self.job.start()
        if settings.LOCAL_TESTING:
            self.job.mark_completed()
            return True
        if self.workflow_job.status == ServiceJobStatusChoices.CREATED:
            self.workflow_job.start()
        try:
            self.execute()
            self.post_execution()
            self.job.mark_completed()

            logger.info(f"{self.job_details} completed")
        except SoftTimeLimitExceeded:
            msg = f"{self.job_details} cancelled as it exceeded the soft time limit. (long job)"
            logger.error(msg)
            self.job.mark_failed(msg)
        except BaseGPUServiceException as e:
            logger.error(f"{self.job_details} failed with error: {str(e)}")
            self.job.mark_failed(str(e))
        except NoRemoteAPIModelServiceException as e:
            logger.error(f"{self.job_details} failed with error: {str(e)} - Retrying in 10 minutes")
            self.job.mark_failed(str(e))
            self.task.retry(countdown=600)
        except Exception as e:
            logger.exception(f"{self.job_details} failed with error: {str(e)}")
            self.job.mark_failed(str(e))

        logger.info(f"{self.job.name}[{self.job.id}] - Time taken: {timezone.now() - self.job.created_at}")

    def execute(self):
        pass

    def post_execution(self):
        if self.index_publish_enabled and self.indexing_event_type is not None:
            IndexPublisher().publish_message(self.indexing_event_type, self.parley_id, self.customer_id, self.language)
            logger.info(f"Published indexing event {self.indexing_event_type} for {self.job_details}")

    @property
    def job_details(self):
        return f"{self.job.name}[{self.job.id}] - [{self.job.parley_id, self.job.customer_id, self.job.language}]"


class RedFlagTask(BaseTask):
    def execute(self):
        task_state = TaskStateModel.objects.get_task_state(self.parley_id, self.customer_id, self.language)
        current_red_flag = {}

        with transaction.atomic():
            # get red-flag from db (if any)
            obj = (
                ParleyTranscription.objects.for_customer(self.customer_id)
                .filter(parley_id=self.parley_id, language=self.language)
                .first()
            )
            if obj:
                if obj.red_flag is not None:
                    current_red_flag = obj.red_flag

            if task_state is not None:
                if task_state.state.get("current_state") is not None:
                    update_nested_dict(current_red_flag, task_state.state.get("current_state"))
                    # Update parley_transcription with updated red-flag
                    ParleyTranscription.objects.for_customer(self.customer_id).filter(
                        parley_id=self.parley_id, language=self.language
                    ).update(red_flag=current_red_flag, updated_at=timezone.now())
                    task_state.delete()

            logger.info(f"Updated existing red flag row for [{self.parley_id}, {self.customer_id}, {self.language}]")


class GPUTask(BaseTask):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = kwargs.get("service")
        if self.service is None:
            raise ValueError("Missing task or service kwarg")
        self.gpu_worker = self.task.app.gpu_worker
        self.app_service = self.task.app.service
        logger.info(f"GPU task details: {self.task.app} - {self.task.name} - {self.gpu_worker} - {self.app_service}")
        self.job.name = self.service.SERVICE_NAME
        self.job.worker = self.gpu_worker
        self.job.type = self.service.SERVICE_JOB_TYPE

        logger.info(f"Updated job {self.job_details}]")

    def execute(self):
        logger.debug(GPUMemoryDetails())
        service = self.service(self.app_service, job=self.job)
        self.job.service_configuration = service.coda_service.configuration
        self.job.service_configuration_settings = service.coda_service.configuration.service_settings
        self.job.transcription_retrieved_from_s3 = service.coda_service.configuration.transcription_settings.reuse_existing
        self.job.save()
        service.start(self.job.parley_id, self.job.customer_id, self.job.language, self.service_metadata)
        logger.debug(GPUMemoryDetails())


class ApiGPUTask:
    def __init__(self, *args, **kwargs):
        self.task = kwargs.get("task")
        if self.task is None:
            raise ValueError("Missing task kwarg")
        self.service = kwargs.get("service")
        if self.service is None:
            raise ValueError("Missing task or service kwarg")
        self.app_service = self.task.app.service

    def run(self, *args, **kwargs):
        start_time = timezone.now()
        if settings.LOCAL_TESTING:
            return True
        try:
            logger.debug(GPUMemoryDetails())
            service = self.service(self.app_service)
            service.start(*args, **kwargs)
            logger.debug(GPUMemoryDetails())

            logger.info("Task completed")
        except SoftTimeLimitExceeded:
            msg = "Task cancelled as it exceeded the soft time limit. (long job)"
            logger.error(msg)
        except BaseGPUServiceException as e:
            logger.exception(f"Task failed with error: {str(e)}")
        except Exception as e:
            logger.exception(f"Task failed with error: {str(e)}")

        logger.info(f"Task - Time taken: {timezone.now() - start_time}")
