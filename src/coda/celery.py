"""
celery -A coda.celery worker -l DEBUG -Q default --max-tasks-per-child=50
celery -A coda.celery worker -l DEBUG --concurrency 1 -Q summary --max-tasks-per-child=20
celery -A coda.celery worker -l DEBUG --concurrency 1 -Q transcription --max-tasks-per-child=20
celery -A coda.celery worker -l DEBUG --concurrency 1 -Q redflag --max-tasks-per-child=20
"""

import logging
from logging.config import dictConfig

from django.conf import settings
from celery import Celery
from celery.signals import setup_logging

from kombu import Queue, Exchange


@setup_logging.connect
def config_loggers(*args, **kwargs):
    dictConfig(settings.LOGGING)


logger = logging.getLogger(__name__)

app = Celery("coda")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object("django.conf:settings", namespace="CELERY")

app.autodiscover_tasks()

app.conf.task_default_queue = "default"
# queue_args = {"x-max-priority": 20, "x-queue-type": "quorum"}
queue_args = {"x-max-priority": 20, "x-ha-policy": "all"}

default_exchange = Exchange("coda", type="direct")

app.conf.task_queues = (
    Queue("default", exchange=default_exchange, routing_key="task.#", queue_arguments=queue_args),
    Queue("summary", exchange=default_exchange, routing_key="summary.#", queue_arguments=queue_args),
    Queue("transcription", exchange=default_exchange, routing_key="transcription.#", queue_arguments=queue_args),
    Queue("redflag", exchange=default_exchange, routing_key="redflag.#", queue_arguments=queue_args),
)
app.conf.task_default_exchange = "coda"
app.conf.task_default_exchange_type = "direct"
app.conf.task_default_routing_key = "task.default"
app.conf.result_expires = 3600
app.conf.update(
    worker_pool_restarts=True,
    # allow 30 minutes for larger models to load when initialising a worker
    worker_proc_alive_timeout=1800,
    # Only prefetch next 1 message after processing
    # might slow us down but will allow for better scaling
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    broker_connection_timeout=5,
    broker_connection_retry=True,
    broker_connection_retry_on_startup=True,
    broker_connection_max_retries=1000,
    broker_channel_error_retry=True,
)
if not settings.LOCAL_TESTING:
    app.conf.broker_use_ssl = True


# # Periodic tasks example:
# @app.on_after_configure.connect
# def setup_periodic_tasks(sender, **kwargs):
#     sender.add_periodic_task(3600.0, test.s('periodic task every hour'), name='add every 10')
#
# @app.task
# def test(arg):
#     logger.info(arg)
#     return arg


# # Custom panel command example
# @Panel.register
# def load_config(*args, **kwargs):
#     """
#     Custom panel command to dynamically load configuration for a worker.
#     Broadcast this command to workers using Celery control.
#     """
#     logger.info("Custom command just ran! args: %r, kwargs: %r" % (args, kwargs))
#     # app = args[0].get("app")
#     # gpu_worker = getattr(app, 'gpu_worker', "")
#     # logger.info(f"service configuration: {gpu_worker.service_configuration}")


#
#
# def broadcast_command_example():
#     """
#     Example usage of broadcasting a command to all workers.
#     Call `load_config` using Celery's control.broadcast.
#     """
#     app.control.broadcast("load_config", arguments={"arg1": "example_value"})
#     logger.info("Broadcasting load_config command with argument 'example_value'.")
#     """
#     """
#     logger.info('Custom command just ran! args: %r, kwargs: %r' % (args, kwargs))
#     app = args[0].get('app')
#     gpu_worker = getattr(app, 'gpu_worker', "")
#     logger.info(f"service configuration: {gpu_worker.service_configuration}")
#     logger.info(f"{app.worker_type}")
