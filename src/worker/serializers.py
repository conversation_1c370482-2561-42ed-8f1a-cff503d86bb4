import time
from functools import lru_cache

from celery.app.control import Control
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from rest_framework.fields import SerializerMethodField

from coda.celery import app
from worker.models import WorkerHost, WorkerHostStatusChoices, ServiceJob, WorkflowJob

UNHEALTHY_STATUS = "unhealthy"
CACHE_TIMEOUT = 45


# Cache to avoid overloading workers with ping
@lru_cache(maxsize=128)
def get_celery_workers_health():
    try:
        results = {}
        control = Control(app)
        ping_response = control.ping()
        if ping_response:
            for worker_status in ping_response:
                for worker, status in worker_status.items():
                    results[worker] = status
            return results
        return None
    except Exception:
        return None


# Add a wrapper to clear cache periodically
def cached_get_workers_health():
    current_time = time.time()
    if not hasattr(cached_get_workers_health, "last_called"):
        cached_get_workers_health.last_called = 0
    if current_time - cached_get_workers_health.last_called > CACHE_TIMEOUT:
        get_celery_workers_health.cache_clear()
        cached_get_workers_health.last_called = current_time
    return get_celery_workers_health()


def get_worker_status(worker_hostname):
    worker_statuses = cached_get_workers_health()
    if worker_statuses is not None:
        status = worker_statuses.get(f"celery@{worker_hostname}")
        if status is not None:
            return status
    return UNHEALTHY_STATUS


class WorkerHostSerializer(serializers.ModelSerializer):
    healthy = serializers.SerializerMethodField()
    ping_status = serializers.SerializerMethodField()

    class Meta:
        model = WorkerHost
        fields = "__all__"

    @extend_schema_field(serializers.BooleanField())
    def get_healthy(self, obj):
        if obj.down_for_maintenance:
            return True  # Skip health is down for maintenance - do not alert monitoring
        return (
            obj.status in [WorkerHostStatusChoices.RUNNING, WorkerHostStatusChoices.LOADING]
            and get_worker_status(obj.hostname) != UNHEALTHY_STATUS
        )

    @extend_schema_field(serializers.CharField())
    def get_ping_status(self, obj):
        return get_worker_status(obj.hostname)


class ServiceJobSerializer(serializers.ModelSerializer):
    class Meta:
        model = ServiceJob
        fields = [
            "id",
            "name",
            "type",
            "worker",
            "service_configuration",
            "workflow",
            "parley_id",
            "customer_id",
            "language",
            "status",
            "error",
            "transcription_retrieved_from_s3",
            "completed_at",
            "failed_at",
        ]
        read_only_fields = fields


class WorkflowJobSerializer(serializers.ModelSerializer):
    service_jobs = ServiceJobSerializer(many=True, read_only=True)  # Assuming reverse relation
    processing_lag = SerializerMethodField()
    processing_lag_percentage = SerializerMethodField()
    duration = SerializerMethodField()
    service_duration = SerializerMethodField()

    class Meta:
        model = WorkflowJob
        fields = [
            "id",
            "parley_id",
            "customer_id",
            "language",
            "requested_services",
            "status",
            "started_at",
            "completed_at",
            "processing_lag",
            "processing_lag_percentage",
            "duration",
            "service_duration",
            "status",
            "service_jobs",
        ]
        read_only_fields = fields

    def get_processing_lag(self, obj):
        if obj.processing_lag:
            return obj.processing_lag.total_seconds()
        return None

    def get_processing_lag_percentage(self, obj):
        if obj.processing_lag_percentage:
            return obj.processing_lag_percentage
        return None

    def get_duration(self, obj):
        if obj.duration:
            return obj.duration.total_seconds()
        return None

    def get_service_duration(self, obj):
        if obj.total_service_duration:
            return obj.total_service_duration.total_seconds()
        return None
