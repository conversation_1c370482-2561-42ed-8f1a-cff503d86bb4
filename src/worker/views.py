from rest_framework.filters import Ordering<PERSON>ilter
from rest_framework.generics import ListAPIView
from rest_framework.viewsets import ReadOnlyModelViewSet
from django_filters.rest_framework import DjangoFilterBackend

from api.permissions import PublicViewMixin
from worker.serializers import WorkerHostSerializer, WorkflowJobSerializer, ServiceJobSerializer
from worker.models import WorkerHost, WorkflowJob, ServiceJob


class WorkerHostListAPIView(PublicViewMixin, ListAPIView):
    """
    API endpoint that returns a read-only list of WorkerHost objects in JSON.
    For health monitoring purposes ALWAYS check the 'healthy' attribute of
    each WorkerHost.
    If the 'healthy' attribute is False, the WorkerHost is unhealthy further
    examination is needed on the 'status' and 'ping_response' attributes.
    """

    queryset = WorkerHost.objects.all()
    serializer_class = WorkerHostSerializer


# Read-only ViewSet for WorkflowJob
class WorkflowJobViewSet(ReadOnlyModelViewSet):
    queryset = WorkflowJob.objects.with_lag().prefetch_related("service_jobs")
    serializer_class = WorkflowJobSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ["status", "language", "parley_id", "customer_id"]  # Fields enabled for filtering
    ordering_fields = ["started_at", "completed_at", "status"]  # Fields enabled for ordering
    ordering = ["-started_at"]  # Default ordering


# Read-only ViewSet for ServiceJob
class ServiceJobViewSet(ReadOnlyModelViewSet):
    queryset = ServiceJob.objects.all()
    serializer_class = ServiceJobSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ["type", "status", "workflow", "worker"]  # Fields enabled for filtering
    ordering_fields = ["completed_at", "failed_at", "status"]  # Fields enabled for ordering
    ordering = ["-completed_at"]  # Default ordering
