import logging
from coda.base_tasks import BaseTaskWithRetry
from coda.celery import app
from common.rabbitmq import IndexPublisher
from common.tenant_clients import TenantHelper
from worker.models import WorkflowJob, ServiceJobStatusChoices

logger = logging.getLogger(__name__)


@app.task(bind=True, queue="default", priority=1)
def calculate_power_consumption(self, workflow_job_id, recalculate=False):
    """
    Calculate the total power consumption of all service jobs in the workflow,
    considering concurrency and overlapping jobs on shared workers.
    :param workflow_job: The workflow job to calculate power consumption for.
    :return: Total power consumption for the workflow (in watt-hours).
    """
    logger.info(f"Starting Power consumption calculations for workflow {workflow_job_id}")
    workflow_job = WorkflowJob.objects.get(id=workflow_job_id)
    if (workflow_job.power_consumption_wh is None or workflow_job.power_consumption_wh == 0.0) or recalculate:
        power_consumption = workflow_job.calculate_power_consumption()
        logger.info(f"Power consumption for workflow {workflow_job.id}: {power_consumption} Wh")
        workflow_job.power_consumption_wh = power_consumption
        workflow_job.save()
    else:
        logger.info(f"Power consumption for workflow {workflow_job.id} already calculated")


@app.task(bind=True, base=BaseTaskWithRetry, queue="default", priority=19)
def post_work_task(self, *args, **kwargs):
    """
    Task to ba called after a workflow is completed.
    """
    workflow_metadata = self.get_workflow_metadata(*args, **kwargs)
    parley_id = workflow_metadata.get("parley_id")
    customer_id = workflow_metadata.get("customer_id")
    language = workflow_metadata.get("language")
    service_metadata = workflow_metadata.get("service_metadata", {})

    workflow_job = WorkflowJob.objects.get(id=workflow_metadata["workflow_job_id"])

    try:
        calculate_power_consumption.apply_async(args=[workflow_job.id, True], countdown=120)
    except Exception as e:
        logger.exception(f"Error creating task for calculating power consumption: {str(e)}")

    logger.info(f"Starting post_work_task for parley id {parley_id} customer_id {customer_id} - {language}")

    index_publish_enabled = workflow_metadata.get("index_publish_enabled", False)
    if index_publish_enabled:
        # Notify that a parley has been processed
        logger.info(f"Publishing message for parley id {parley_id} customer_id {customer_id} - {language}")
        IndexPublisher().publish_message("coda-pipeline_completed", parley_id, customer_id, language)

    if workflow_job.service_jobs.filter(status=ServiceJobStatusChoices.FAILED).exists():
        if service_metadata.get("update_coda_work_status", True):
            TenantHelper(parley_id, customer_id, language).update_coda_work_status(254)
        workflow_job.mark_failed()
    else:
        if service_metadata.get("update_coda_work_status", True):
            TenantHelper(parley_id, customer_id, language).update_coda_work_status(255)
        workflow_job.mark_completed()
    logger.info(
        f"Finished post_work_task for parley id {workflow_metadata.get('parley_id')} "
        f"customer_id {workflow_metadata.get('customer_id')} - {workflow_metadata.get('language')}"
    )
