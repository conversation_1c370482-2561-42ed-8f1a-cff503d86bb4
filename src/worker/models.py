import logging

from django.db import models
from django.db.models import F, <PERSON><PERSON><PERSON><PERSON>, Case, When, Value, Sum, ExpressionWrapper, FloatField, OuterRef, \
    Subquery, Count, Q
from django.utils import timezone

from service.models import ServiceConfiguration
from coda.models import UUIDModel, CreatedMixin, BaseModel, CompletedMixin, StartedMixin, FailedMixin

logger = logging.getLogger(__name__)


class WorkerHostStatusChoices(models.TextChoices):
    RUNNING = "running"
    STOPPED = "stopped"
    LOADING = "loading"


class WorkerHost(BaseModel):
    hostname = models.CharField(max_length=64)
    ec2_type = models.CharField(max_length=64, default="unknown")
    name = models.CharField(max_length=130, unique=True)
    description = models.TextField(blank=True, null=True)
    # running = models.BooleanField(default=False)
    service_configuration_bypass = models.ForeignKey(
        ServiceConfiguration, on_delete=models.CASCADE, related_name="workers", blank=True, null=True
    )
    llm_model = models.ForeignKey("llm_model.LLMModel", on_delete=models.SET_NULL, null=True, default=None)
    status = models.CharField(
        max_length=20, choices=WorkerHostStatusChoices.choices, default=WorkerHostStatusChoices.STOPPED
    )
    down_for_maintenance = models.BooleanField(default=False)
    gpu_tdp_watts = models.IntegerField(default=0)

    @property
    def running(self):
        return self.status == WorkerHostStatusChoices.RUNNING

    def start(self):
        self.status = WorkerHostStatusChoices.RUNNING
        # self.running = True
        self.save()

    def loading(self):
        self.status = WorkerHostStatusChoices.LOADING
        # self.running = False
        self.save()

    def stop(self):
        self.status = WorkerHostStatusChoices.STOPPED
        # self.running = False
        self.save()


class ServiceJobType(models.TextChoices):
    SUMMARY = "summary"
    TRANSCRIPTION = "transcription"
    REDFLAG = "redflag"
    OTHER = "other"


class ServiceJobStatusChoices(models.TextChoices):
    CREATED = "created"
    STARTED = "started"
    COMPLETED = "completed"
    FAILED = "failed"


class WorkflowJobQuerySet(models.QuerySet):
    def with_lag(self):
        return self.with_processing_lag().with_processing_lag_percentage_rounded()

    def with_processing_lag(self):
        """Annotate processing lag as total_service_duration - duration."""
        return self.annotate(
            processing_lag=ExpressionWrapper(
                F("duration") - F("total_service_duration"),
                output_field=DurationField(),
            )
        )

    def with_processing_lag_percentage_rounded(self):
        """Annotate processing lag percentage as total_service_duration / duration."""
        return self.annotate(
            processing_lag_percentage=ExpressionWrapper(
                100 - F("total_service_duration") / F("duration") * 100, output_field=FloatField()
            )
        )

    def with_duration(self):
        return self.annotate(
            duration=Case(
                When(created_at__isnull=True, then=Value(0)),
                When(completed_at__isnull=False, then=(F("completed_at") - F("created_at"))),
                default=Value(0),  # If no timestamps are available
                output_field=DurationField(),
            )
        )

    def with_total_service_duration(self):
        """Annotate total duration based on conditions."""
        return self.annotate(
            total_service_duration=Case(
                When(
                    total_service_duration_summary__gt=F("duration"),
                    then=F("duration"),
                ),
                default=F("total_service_duration_summary"),
                output_field=DurationField(),
            )
        )


    def with_total_service_duration_summary(self):
        """Annotate total duration summary from related service jobs."""
        return self.annotate(
            total_service_duration_summary=Sum(
                Case(
                    When(
                        service_jobs__completed_at__isnull=False,
                        then=(F("service_jobs__completed_at") - F("service_jobs__created_at")),
                    ),
                    When(
                        service_jobs__failed_at__isnull=False,
                        then=(F("service_jobs__failed_at") - F("service_jobs__created_at")),
                    ),
                    default=Value(0),
                    output_field=DurationField(),
                )
            )
        )

    def with_service_statuses(self):
        """Return workflow jobs with lists of completed, pending, and failed service jobs."""
        return self.prefetch_related("service_jobs")

    def get_service_statuses(self):
        """Returns a dictionary of completed, pending, and failed service jobs."""
        workflows = self.with_service_statuses()
        data = []
        for workflow in workflows:
            completed = [
                (str(s.id), s.name, s.status)
                for s in workflow.service_jobs.all()
                if s.status == ServiceJobStatusChoices.COMPLETED
            ]
            pending = [
                (str(s.id), s.name, s.status)
                for s in workflow.service_jobs.all()
                if s.status == ServiceJobStatusChoices.STARTED
            ]
            failed = [
                (str(s.id), s.name, s.status)
                for s in workflow.service_jobs.all()
                if s.status == ServiceJobStatusChoices.FAILED
            ]

            data.append(
                {
                    "workflow_id": workflow.id,
                    "completed_services": completed,
                    "pending_services": pending,
                    "failed_services": failed,
                }
            )
        return data


class WorkflowManager(models.Manager.from_queryset(WorkflowJobQuerySet)):
    def get_queryset(self):
        return (
            super(WorkflowManager, self)
            .get_queryset()
            .with_duration()
            .with_total_service_duration_summary()
            .with_total_service_duration()
            .with_service_statuses()
        )

    def with_service_statuses(self):
        return self.get_queryset().with_service_statuses()

    def get_service_statuses(self):
        return self.get_queryset().get_service_statuses()


class WorkflowJob(StartedMixin, CompletedMixin, BaseModel):
    parley_id = models.IntegerField()
    customer_id = models.IntegerField()
    language = models.CharField(max_length=20)
    requested_services = models.TextField(default="")
    status = models.CharField(max_length=20, choices=ServiceJobStatusChoices.choices)
    power_consumption_wh = models.FloatField(default=0.0)

    objects = WorkflowManager()

    def start(self):
        self.started_at = timezone.now()
        self.status = ServiceJobStatusChoices.STARTED
        self.save()

    def mark_completed(self):
        self.completed_at = timezone.now()
        self.status = ServiceJobStatusChoices.COMPLETED
        self.save()

    def mark_failed(self, error_message="Unknown Error"):
        self.completed_at = timezone.now()
        self.error = error_message
        self.status = ServiceJobStatusChoices.FAILED
        self.save()

    def calculate_power_consumption(self):
        """
        Calculate the total power consumption of all service jobs in the workflow,
        considering concurrency and overlapping jobs on shared workers.
        :return: Total power consumption for the workflow (in watt-hours).
        """
        # Fetch all service jobs in the current workflow with related workers
        workflow_service_jobs = ServiceJob.objects.filter(workflow=self).select_related("worker")

        # Optimize overlapping job calculation
        overlapping_jobs = (
            ServiceJob.objects.filter(
                Q(worker=OuterRef("worker")),
                Q(created_at__isnull=False),
                Q(completed_at__isnull=False),
                Q(created_at__lte=OuterRef("completed_at")),
                Q(completed_at__gte=OuterRef("created_at")),
            )
            .values("worker")
            .annotate(num_jobs=Count("id"))  # Precompute concurrency by counting overlapping jobs
            .values("num_jobs")
        )

        # Annotate each job with concurrency
        workflow_service_jobs = workflow_service_jobs.annotate(
            concurrency=Subquery(overlapping_jobs, output_field=models.IntegerField())
        )

        # Compute total power consumption
        total_power_consumption = 0.0
        for job in workflow_service_jobs:
            if not job.created_at or not job.completed_at or not job.worker:
                logger.warning(f"Skipping job {job.id} due to missing timestamps or no associated worker")
                continue

            # Use the GPU TDP from the worker
            gpu_max_power = job.worker.gpu_tdp_watts

            # Use pre-computed concurrency or default to 1
            concurrency = max(job.concurrency or 1, 1)

            # Calculate runtime in hours
            runtime = (job.completed_at - job.created_at).total_seconds() / 3600.0

            # Calculate and accumulate power consumption
            job_power = (gpu_max_power / concurrency) * runtime
            total_power_consumption += job_power

        return total_power_consumption


class ServiceJobQuerySet(models.QuerySet):
    def with_duration(self):
        return self.annotate(
            duration=Case(
                When(completed_at__isnull=False, then=(F("completed_at") - F("created_at"))),
                When(failed_at__isnull=False, then=(F("failed_at") - F("created_at"))),
                When(created_at__isnull=False, then=Value(0)),
                default=Value(0),  # If no timestamps are available
                output_field=DurationField(),
            )
        )

    def with_power_consumption(self, gpu_power_consumption):
        """
        Annotate power consumption for each job based on concurrency and GPU worker's power consumption.
        :param gpu_power_consumption: The maximum power consumption of the GPU in watts.
        """
        # Subquery to calculate the number of jobs overlapping on the same worker
        concurrent_jobs = ServiceJob.objects.filter(
            worker=OuterRef("worker"),  # Filter jobs on the same worker
            created_at__lte=OuterRef("completed_at"),  # Overlapping jobs
            completed_at__gte=OuterRef("created_at")
        ).values("worker").annotate(
            num_jobs=Count("id")  # Count concurrent jobs
        ).values("num_jobs")

        # Calculate power consumption based on the number of concurrent jobs
        power_per_job = ExpressionWrapper(
            gpu_power_consumption / Subquery(concurrent_jobs),  # Power distribution
            output_field=FloatField()
        )

        return self.annotate(
            num_concurrent_jobs=Subquery(concurrent_jobs),  # Annotate concurrency
            power_consumption=power_per_job  # Annotate power consumption
        )


class ServiceJobManager(models.Manager.from_queryset(ServiceJobQuerySet)):
    def get_queryset(self):
        return super(ServiceJobManager, self).get_queryset().with_duration()


class ServiceJob(FailedMixin, CompletedMixin, BaseModel):
    name = models.CharField(max_length=255, default="task")
    type = models.CharField(max_length=20, choices=ServiceJobType.choices)
    worker = models.ForeignKey(WorkerHost, on_delete=models.CASCADE, related_name="service_jobs", null=True, blank=True)
    service_configuration = models.ForeignKey(
        ServiceConfiguration, on_delete=models.CASCADE, related_name="service_jobs", null=True, blank=True
    )
    service_configuration_settings = models.JSONField(default=dict, null=True, blank=True)

    workflow = models.ForeignKey(
        WorkflowJob, on_delete=models.CASCADE, null=True, blank=True, related_name="service_jobs"
    )
    parley_id = models.IntegerField()
    customer_id = models.IntegerField()
    language = models.CharField(max_length=20)
    transcription_retrieved_from_s3 = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=ServiceJobStatusChoices.choices)
    error = models.TextField(blank=True, null=True)

    objects = ServiceJobManager()

    def start(self):
        self.status = ServiceJobStatusChoices.STARTED
        self.save()

    def mark_completed(self):
        self.completed_at = timezone.now()
        self.status = ServiceJobStatusChoices.COMPLETED
        self.save()

    def mark_failed(self, error_message="Unknown Error"):
        self.failed_at = timezone.now()
        self.error = error_message
        self.status = ServiceJobStatusChoices.FAILED
        self.save()


class MessengerWorkerConfiguration(UUIDModel):
    """Store the configuration for the worker."""

    # these values match the python logging module values
    LOG_LEVEL_DEBUG = 10
    LOG_LEVEL_INFO = 20
    LOG_LEVEL_WARNING = 30
    LOG_LEVEL_ERROR = 40
    LOG_LEVEL_CRITICAL = 50
    LOG_LEVEL_CHOICES = [
        (LOG_LEVEL_DEBUG, "DEBUG"),
        (LOG_LEVEL_INFO, "INFO"),
        (LOG_LEVEL_WARNING, "WARNING"),
        (LOG_LEVEL_ERROR, "ERROR"),
        (LOG_LEVEL_CRITICAL, "CRITICAL"),
    ]

    name = models.CharField(max_length=255, unique=True)
    is_enabled = models.BooleanField(default=False)
    sleep_seconds = models.IntegerField(default=3)
    incremental_sleep = models.BooleanField(default=False)
    incremental_seconds_max = models.IntegerField(default=10)
    rate_limit_enabled = models.BooleanField(default=False)
    rate_limit_max_messages = models.IntegerField(default=10)
    rate_limit_time_window = models.IntegerField(default=60)
    rate_limit_delay_seconds = models.IntegerField(default=30)
    index_publish_enabled = models.BooleanField(default=False)
    consuming = models.BooleanField(default=False)

    log_level = models.IntegerField(
        choices=LOG_LEVEL_CHOICES,
        default=LOG_LEVEL_WARNING,
    )

    custom = models.JSONField(default=dict, null=True, blank=True)

    notes = models.TextField(blank=True, default="")

    def __str__(self) -> str:
        """Return the worker name."""
        return self.name


class MessengerWorkerError(CreatedMixin, UUIDModel):
    """Store the errors encountered by the worker."""

    ERROR_OPEN = "O"
    ERROR_CLOSED = "C"
    ERROR_STATUS_CHOICES = ((ERROR_OPEN, "Open"), (ERROR_CLOSED, "Closed"))

    error = models.TextField()
    error_status = models.CharField(max_length=1, choices=ERROR_STATUS_CHOICES, default=ERROR_OPEN)

    worker = models.ForeignKey(
        "MessengerWorkerConfiguration",
        on_delete=models.CASCADE,
        related_name="worker_errors",
        null=True,
        blank=True,
    )

    class Meta:
        """Meta options for the model."""

        ordering = ["-created_at"]

    def __str__(self) -> str:
        """Return the worker name."""
        return self.worker.name
