from django import forms
from django.contrib import admin, messages
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.http import HttpResponseRedirect
from django.shortcuts import render
from django.urls import reverse, path
from django.utils.html import format_html
from django_json_widget.widgets import <PERSON><PERSON><PERSON><PERSON>orWidget

from coda.celery import app
from worker.models import WorkerHost, MessengerWorkerConfiguration, MessengerWorkerError, ServiceJob, WorkflowJob

from django_celery_results.models import TaskResult, GroupResult

# Unregister TaskResult from Admin
admin.site.unregister(TaskResult)
admin.site.unregister(GroupResult)


class ReadOnlyAdmin(admin.ModelAdmin):
    readonly_fields = []

    def get_readonly_fields(self, request, obj=None):
        return (
            list(self.readonly_fields)
            + [field.name for field in obj._meta.fields]
            + [field.name for field in obj._meta.many_to_many]
        )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return True


class ReadOnlyTabularInline(admin.TabularInline):
    extra = 0
    can_delete = False
    editable_fields = []
    readonly_fields = []
    exclude = []

    def get_readonly_fields(self, request, obj=None):
        return list(self.readonly_fields) + [
            field.name
            for field in self.model._meta.fields
            if field.name not in self.editable_fields and field.name not in self.exclude
        ]

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.action(description="Worker pool restart")
def restart_pool(modeladmin, request, queryset):
    for obj in queryset:
        app.control.broadcast("pool_restart", destination=[f"celery@{obj.hostname}"])
        messages.add_message(request, messages.INFO, "Worker pool restarting...")


# @admin.register(WorkerHost)
# class WorkerHostAdmin(admin.ModelAdmin):
#     """Admin interface for WorkerHost."""
#
#     list_display = ("name", "hostname", "status", "down_for_maintenance", "llm_model_link","service_configuration_bypass__name")
#     actions = [
#         restart_pool,
#     ]
#
#     def llm_model_link(self, obj):
#         """Create a hyperlink for the llm_model field."""
#         if obj.llm_model:
#             url = reverse(
#                 "admin:llm_model_llmmodel_change",
#                 args=[obj.llm_model.id],
#             )
#             return format_html('<a href="{}">{}</a>', url, obj.llm_model.model_name)
#         return "-"
#
#     llm_model_link.short_description = "LLM Model"
#     llm_model_link.admin_order_field = "llm_model__model_name"
#
#     def save_model(self, request, obj, form, change):
#         super().save_model(request, obj, form, change)
#         if change:
#             try:
#                 app.control.broadcast("pool_restart", destination=[f"celery@{obj.hostname}"])
#                 messages.add_message(request, messages.INFO, "Worker pool restarting to reflect new configuration...")
#             except Exception:
#                 messages.add_message(request, messages.ERROR, "Unable to restart worker pool")


#####################################################################################


# Define a form to get the 'count' from the admin user
class PoolUpdateForm(forms.Form):
    count = forms.IntegerField(
        label="Number of processes",
        required=True,
        min_value=1,
        help_text="Enter the number of worker processes to add or remove."
    )


@admin.register(WorkerHost)
class WorkerHostAdmin(admin.ModelAdmin):
    """Admin interface for WorkerHost."""

    list_display = ("name", "hostname", "status", "current_concurrency", "down_for_maintenance", "llm_model_link",
                    "service_configuration_bypass__name")
    actions = [
        "grow_pool_action",
        "shrink_pool_action",
        "restart_pool_action",
    ]
    update_pool_form = PoolUpdateForm

    # --- Custom Column to Display Pool Size ---
    def current_concurrency(self, obj):
        """Inspect the worker to get its current max-concurrency."""
        try:
            # Ping a specific worker to be efficient
            inspector = app.control.inspect([f"celery@{obj.hostname}"])
            stats = inspector.stats()
            if not stats:
                return format_html('<span style="color: #orange;">Offline</span>')

            worker_stats = stats.get(f"celery@{obj.hostname}", {})
            pool_info = worker_stats.get("pool", {})
            concurrency = len(pool_info.get("processes"))

            if concurrency is not None:
                return concurrency
            return format_html('<span style="color: #ccc;">N/A</span>')
        except Exception:
            return format_html('<span style="color: #red;">Error</span>')

    current_concurrency.short_description = "Pool Size"
    current_concurrency.admin_order_field = None  # Cannot be sorted

    # --- Methods for the Intermediate Form Page ---
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                'update-pool/',
                self.admin_site.admin_view(self.update_pool_view),
                name='update_worker_pool',  # Name for reversing
            )
        ]
        return custom_urls + urls

    def update_pool_view(self, request):
        """
        A view that displays a form to get the 'count' for growing/shrinking
        the worker pool and then executes the command.
        """
        context = dict(self.admin_site.each_context(request))
        action = request.session.get('pool_action')
        worker_ids = request.session.get('pool_worker_ids')

        if not action or not worker_ids:
            messages.error(request, "Could not find an action to perform. Please try again.")
            return HttpResponseRedirect("../")

        queryset = self.get_queryset(request).filter(pk__in=worker_ids)

        if request.method == 'POST':
            form = self.update_pool_form(request.POST)
            if form.is_valid():
                count = form.cleaned_data['count']
                destinations = [f"celery@{obj.hostname}" for obj in queryset]

                try:
                    command = None
                    if action == 'grow':
                        command = app.control.pool_grow
                        verb = "grow"
                    elif action == 'shrink':
                        command = app.control.pool_shrink
                        verb = "shrink"

                    if command:
                        command(n=count, destination=destinations)
                        self.message_user(request,
                                          f"Successfully sent command to {verb} pool by {count} for {len(destinations)} worker(s).",
                                          messages.SUCCESS)

                except Exception as e:
                    self.message_user(request, f"Error sending command to workers: {e}", messages.ERROR)

                # Clean up session and redirect back
                del request.session['pool_action']
                del request.session['pool_worker_ids']
                return HttpResponseRedirect("../")
        else:
            form = self.update_pool_form()

        context['form'] = form
        context['workers'] = queryset
        context['action_name'] = "Grow" if action == 'grow' else "Shrink"
        context['title'] = f"{context['action_name']} Worker Pool"

        return render(request, 'admin/update_pool_form.html', context)

    # --- Admin Actions ---
    @admin.action(description="Worker pool: Grow")
    def grow_pool_action(self, request, queryset):
        request.session['pool_action'] = 'grow'
        # Convert UUIDs to strings before storing in session
        worker_pks = [str(pk) for pk in queryset.values_list('pk', flat=True)]
        request.session['pool_worker_ids'] = worker_pks
        return HttpResponseRedirect("update-pool/")

    @admin.action(description="Worker pool: Shrink")
    def shrink_pool_action(self, request, queryset):
        request.session['pool_action'] = 'shrink'
        # Convert UUIDs to strings before storing in session
        worker_pks = [str(pk) for pk in queryset.values_list('pk', flat=True)]
        request.session['pool_worker_ids'] = worker_pks
        return HttpResponseRedirect("update-pool/")

    @admin.action(description="Worker pool: Restart")
    def restart_pool_action(self, request, queryset):
        destinations = [f"celery@{obj.hostname}" for obj in queryset]
        try:
            app.control.broadcast("pool_restart", destination=destinations)
            messages.success(request, f"Restart command sent to {len(destinations)} worker(s).")
        except Exception as e:
            messages.error(request, f"Failed to send restart command: {e}")

    # --- Existing methods ---
    def llm_model_link(self, obj):
        """Create a hyperlink for the llm_model field."""
        if obj.llm_model:
            url = reverse("admin:llm_model_llmmodel_change", args=[obj.llm_model.id])
            return format_html('<a href="{}">{}</a>', url, obj.llm_model.model_name)
        return "-"

    llm_model_link.short_description = "LLM Model"
    llm_model_link.admin_order_field = "llm_model__model_name"

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        if change:
            try:
                app.control.broadcast("pool_restart", destination=[f"celery@{obj.hostname}"])
                messages.add_message(request, messages.INFO, "Worker pool restarting to reflect new configuration...")
            except Exception:
                messages.add_message(request, messages.ERROR, "Unable to restart worker pool")


#####################################################################################

class ServiceJobInline(ReadOnlyTabularInline):  # or admin.StackedInline
    model = ServiceJob
    readonly_fields = ["get_id", "get_duration"]
    extra = 0

    def get_duration(self, obj):
        return int(obj.duration.total_seconds())

    def get_id(self, obj):
        url = reverse("admin:worker_servicejob_change", args=[obj.id])
        return format_html('<a href="{}">{}</a>', url, obj.id)

    get_duration.short_description = "Total time"


@admin.register(WorkflowJob)
class WorkflowJobAdmin(admin.ModelAdmin):
    list_display = (
        "created_at",
        "customer_id",
        "parley_id",
        "requested_services",
        "get_duration",
        "get_service_duration",
        "get_lag_seconds",
        "get_lag_percentage",
        "formatted_power_consumption",
        "status",
    )

    readonly_fields = ("get_duration", "get_service_duration", "get_lag_seconds", "get_lag_percentage", "power_consumption_wh")

    list_filter = ["status", "customer_id"]

    ordering = ["-created_at"]
    search_fields = ("id", "parley_id", "customer_id")
    inlines = [ServiceJobInline]

    def formatted_power_consumption(self, obj):
        """Return power_consumption_wh with 3 decimal points."""
        return f"{obj.power_consumption_wh:.3f}"

    formatted_power_consumption.short_description = "Power Consumption (Wh)"
    formatted_power_consumption.admin_order_field = "power_consumption_wh"

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.with_lag()

    def get_lag_seconds(self, obj):
        return int(obj.processing_lag.total_seconds()) if obj.processing_lag is not None else None

    def get_lag_percentage(self, obj):
        return round(obj.processing_lag_percentage, 2) if obj.processing_lag_percentage is not None else None

    def get_duration(self, obj):
        return int(obj.duration.total_seconds())

    def get_service_duration(self, obj):
        return int(obj.total_service_duration.total_seconds())

    get_lag_seconds.short_description = "Lag seconds"

    get_lag_percentage.short_description = "Lag percentage"

    get_service_duration.short_description = "Total services time"

    get_duration.short_description = "Total time"


@admin.register(ServiceJob)
class ServiceJobAdmin(ReadOnlyAdmin):
    list_display = (
        "id",
        "created_at",
        "customer_id",
        "parley_id",
        "name",
        "type",
        "status",
        "get_worker_link",
        "service_configuration_link",
        "workflow_link",
        "get_duration",
    )
    raw_id_fields = ("workflow", "service_configuration", "worker")

    list_filter = ["customer_id", "type", "status", "name"]
    search_fields = ("id", "parley_id", "customer_id")

    ordering = ["-created_at"]
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }

    def get_duration(self, obj):
        return int(obj.duration.total_seconds())

    get_duration.short_description = "Total time"

    def get_worker_link(self, obj):
        if obj.worker is not None:
            url = reverse("admin:worker_workerhost_change", args=[obj.worker.id])
            return format_html('<a href="{}">{}</a>', url, obj.worker.name)

    def service_configuration_link(self, obj):
        if obj.service_configuration is not None:
            url = reverse("admin:service_serviceconfiguration_change", args=[obj.service_configuration.id])
            return format_html('<a href="{}">{}</a>', url, obj.service_configuration)

    service_configuration_link.short_description = "Configuration"

    def workflow_link(self, obj):
        url = reverse("admin:worker_workflowjob_change", args=[obj.workflow.id])
        return format_html(
            '<a href="{}">{}</a>', url, f"{obj.workflow.parley_id}-{obj.workflow.customer_id}-{obj.workflow.language}"
        )

    workflow_link.short_description = "Workflow"

    def get_worker_name(self, obj):
        return obj.worker.name

    get_worker_name.admin_order_field = "worker"
    get_worker_name.short_description = "Worker Name"


@admin.register(MessengerWorkerConfiguration)
class WorkerConfigurationAdmin(admin.ModelAdmin):
    """Admin interface for WorkerConfiguration."""

    list_display = (
        "name",
        "is_enabled",
        "consuming",
        "sleep_seconds",
        "rate_limit_enabled",
        "log_level",
    )
    list_editable = (
        "is_enabled",
        "sleep_seconds",
        "rate_limit_enabled",
        "log_level",
    )


@admin.register(MessengerWorkerError)
class WorkerErrorAdmin(admin.ModelAdmin):
    """Worker error admin."""

    list_display = (
        "worker",
        "created_at",
        "error_status",
    )
    list_filter = ("error_status",)
    search_fields = ("error",)
    readonly_fields = ("worker", "error", "created_at")
