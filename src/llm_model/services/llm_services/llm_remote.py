import logging
from contextlib import contextmanager

import requests
from django.conf import settings

from llm_model.exceptions import BaseModelServiceException, NoRemoteAPIModelServiceException
from llm_model.models import LLMModel


logger = logging.getLogger(__name__)


class LLMRemoteModelService:
    """
    Model service to be used for calling a remote openai API model
    """
    WORKER_TYPE = "APP"
    OUTPUT_TYPE = "api"
    DEFAULT_LLM_MODEL = "llama_31_8B_v1"
    SUPPORTS_STRUCTURED_OUTPUT = True

    def __init__(self, llm_model: LLMModel = None):
        if settings.WORKER_TYPE != self.WORKER_TYPE:
            raise BaseModelServiceException(
                f"Service needs a {self.WORKER_TYPE} worker. " f"Found {settings.WORKER_TYPE} worker"
            )
        self.cuda_available = False
        # self.coda_service = self._init_coda_service(coda_service)
        self.llm_model = self._init_llm_model(llm_model)
        self.device = "cpu"
        if self.llm_model is None:
            raise BaseModelServiceException(f"LLM model not found: {self.llm_model}")

        self.tokenizer = self._get_tokenizer()

        self.model = self._get_model()

    def _init_llm_model(self, llm_model: LLMModel = None):
        if llm_model is None:
            logger.debug(f"No LLM model provided. Initialising with default {self.DEFAULT_LLM_MODEL} configuration")
            return LLMModel.objects.filter(model_name=self.DEFAULT_LLM_MODEL).first()
        else:
            return llm_model

    # def _init_coda_service(self, coda_service: CODAService = None):
    #     if coda_service is None:
    #         logger.debug(
    #             f"No CODA service provided. Initialising with default {self.CODA_SERVICE} service configuration"
    #         )
    #         return CODAService.objects.filter(name=self.CODA_SERVICE).first()
    #     else:
    #         return coda_service
    #
    # @property
    # def service_configuration(self) -> ServiceConfiguration:
    #     self.coda_service.refresh_from_db()
    #     return self.coda_service.configuration

    @staticmethod
    def _get_tokenizer():
        class RemoteTokenizer:
            """
            Fake tokenizer to be used when calling a remote model with openai API
            """

            pad_token = None
            eos_token_id = None

            def __init__(self, prompt, *args, **kwargs):
                self.input_ids = prompt

            @staticmethod
            def apply_chat_template(prompt, **kwargs):
                return prompt

            @staticmethod
            def decode(tokens, **kwargs):
                return tokens

            @staticmethod
            def convert_tokens_to_ids(tokens, *args, **kwargs):
                return tokens

            def __getitem__(self, item):
                try:
                    return getattr(self, item)
                except AttributeError:
                    return ""

        return RemoteTokenizer

    def _get_model(self):
        class RemoteModel:
            device = "cpu"
            supports_extra_payload = True

            def __init__(self, llm_model: LLMModel, *args, **kwargs):
                self.base_url = "http://localhost:1234"
                self.model_id = llm_model.model_id
                self.model_name = llm_model.model_name
                self.config = {
                    "top_p": 0.8,
                    "temperature": 0.3,
                    "max_completion_tokens": 1000,
                    "presence_penalty": 2.0,
                }
                logger.info(f"Initialised remote model with API url {self.base_url}")

            def eval(self):
                pass

            def set_adapter(self, adapter_name):
                pass

            @contextmanager
            def disable_adapter(self):
                yield

            def generate(self, messages, *args, **kwargs):
                health_url = f"{self.base_url}/health"
                try:
                    health_response = requests.get(health_url)
                    if health_response.status_code != 200:
                        raise NoRemoteAPIModelServiceException(
                            f"Remote API health check failed with status code {health_response.status_code}")
                except requests.exceptions.RequestException as e:
                    raise NoRemoteAPIModelServiceException(f"Failed to connect to remote API health endpoint: {str(e)}")

                api_url = f"{self.base_url}/v1/chat/completions"
                headers = {
                    "Content-Type": "application/json",
                }

                # Prepare payload for the OpenAI API
                payload = {
                    "model": self.model_name,
                    "messages": messages,
                    "temperature": kwargs.get("temperature", self.config["temperature"]),
                    "max_completion_tokens": kwargs.get("max_completion_tokens", self.config["max_completion_tokens"]),
                    "top_p": kwargs.get("top_p", self.config["top_p"]),
                    "presence_penalty": kwargs.get("presence_penalty", self.config["presence_penalty"]),
                    "tools": [],
                    "stream": False,
                }
                payload.update(kwargs.get("extra_payload", {}))

                try:
                    logger.info(f"Calling API {api_url}")

                    response = requests.post(api_url, headers=headers, json=payload)

                    # Check if request was successful
                    if response.status_code == 200:
                        response_data = response.json()
                        # Extract and return the generated text from the response
                        # if "choices" in response_data and len(response_data["choices"]) > 0:
                        #     return response_data["choices"][0]["message"]["content"]

                        return response_data
                    else:
                        # Handle error cases
                        error_message = f"API request failed with status code {response.status_code}: {response.text}"
                        raise BaseModelServiceException(error_message)
                except requests.exceptions.RequestException as e:
                    error_message = f"API request failed with exception {str(e)}"
                    raise BaseModelServiceException(error_message)

        model = RemoteModel(self.llm_model)
        return model
