import logging
from coda.base_tasks import BaseTranscription<PERSON>askWithRetry, GPUTask
from coda.celery import app
from transcription.services import TranscriptionService

logger = logging.getLogger(__name__)


@app.task(
    bind=True,
    queue="transcription",
    base=BaseTranscriptionTaskWithRetry,
    soft_time_limit=3500,
    time_limit=3600,
    priority=15,
)
def transcription_task(self, *args, **kwargs):
    kwargs.update(service=TranscriptionService, task=self, indexing_event_type="transcription_completed")
    task = GPUTask(*args, **kwargs)
    task.run()
