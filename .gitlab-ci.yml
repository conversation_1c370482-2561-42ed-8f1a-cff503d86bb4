stages:
  - pretest
  - build-test
  - test
  - build
  - deploy-staging
  - test-staging
  - cleanup

variables:
  ADMIN_CONTAINER_NAME: 'admingunicorn'
  MESSENGER_CONTAINER_NAME: 'messenger'
  DEFAULTWORKER_CONTAINER_NAME: 'defaultworker'
  FLOWER_CONTAINER_NAME: 'flower'
  NGINX_CONTAINER_NAME: 'nginx'
  TRANSCRIPTION_CONTAINER_NAME: 'transcription'
  LLMGENERIC_CONTAINER_NAME: 'summaryredflag'
  LLMGENERICAPI_CONTAINER_NAME: 'summaryredflagapi'
  ECR_REGISTRY: '785699317897.dkr.ecr.us-east-1.amazonaws.com'
  ECR_REPOSITORY: 'symphony-staging/coda'
  USER_ID: '1004'
  GROUP_ID: '1004'
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""


trivy-scan:
  stage: pretest
  rules:
    - if: $SKIP_TESTS == "true"
      when: never
    - when: always
  image:
    name: aquasec/trivy:latest
    entrypoint: [""]
  script:
    # Create results directory
    - mkdir -p trivy-results

    # Run main scan with JSON output
    - trivy filesystem --format json --output trivy-results/scan-results.json --severity HIGH,CRITICAL .

    # Generate a simple text summary for quick review
    - trivy filesystem --format table --output trivy-results/summary.txt .
  artifacts:
    when: always
    paths:
      - trivy-results/

.base:
  variables:
    FF_NETWORK_PER_BUILD: "true"
    FF_SCRIPT_SECTIONS: "true"
    FF_TIMESTAMPS: "true"

.docker:
  extends: .base
  image: docker:28.0
  services:
    - name: docker:28.0-dind
      command: [ "--dns", "*******", "--dns", "*******" ]
  variables:
    DOCKER_BUILDKIT: 1
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - apk add -q --no-cache curl aws-cli jq
    - >
      for i in $(seq 1 10); do
        docker info && break || {
          echo "Waiting for Docker... ($i/10)";
          sleep 1;
        }
      done
    - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REGISTRY

.docker-build:
  extends: .docker
  cache:
    key: buildx-${CI_PROJECT_NAME}
    paths:
      - /cache/buildx-cache
    policy: pull-push
  before_script:
    - apk add -q --no-cache curl aws-cli
    - >
      for i in $(seq 1 10); do
        docker info && break || {
          echo "Waiting for Docker... ($i/10)";
          sleep 1;
        }
      done
    - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REGISTRY
    - mkdir -p .buildx-cache
    - docker buildx inspect ci_builder || docker buildx create --name ci_builder --use --driver docker-container
    - docker buildx use ci_builder
    - docker buildx inspect --bootstrap

build-test-image:
  stage: build-test
  rules:
    - if: $SKIP_TESTS == "true"
      when: never
    - when: on_success
  extends: .docker-build
  script:
    - >
      docker buildx build --progress=plain \
        --build-arg UID=${USER_ID} \
        --build-arg GID=${GROUP_ID} \
        --build-arg CACHE_BUST=1 \
        --target test \
        --cache-to type=local,dest=/cache/buildx-cache,mode=max \
        --cache-to type=registry,mode=max,image-manifest=true,oci-mediatypes=true,ref=${ECR_REGISTRY}/${ECR_REPOSITORY}/test:cache \
        --cache-from type=local,src=/cache/buildx-cache \
        --cache-from type=registry,ref=${ECR_REGISTRY}/${ECR_REPOSITORY}/test:cache \
        -t ${ECR_REGISTRY}/${ECR_REPOSITORY}/test:${CI_COMMIT_SHA} \
        --push \
        .
  needs: [trivy-scan]

unit-test:
  # sudo ./${SCRIPT_NAME} run ${env.GIT_COMMIT} test --uid 1004 --gid 1004 --no-migration-check
  stage: test
  rules:
    - if: $SKIP_TESTS == "true"
      when: never
    - when: on_success
  extends: .docker
  script:
    - docker rm -f test-runner || true
    - >
      docker run \
        --user $USER_UID:$USER_GID \
        --name test-runner \
        -e WORKER_TYPE=APP \
        -e DEBUG=true \
        -e DJANGO_SETTINGS_MODULE=coda.settings.test \
        --entrypoint /bin/bash \
        -v ${CI_PROJECT_DIR}:/reports \
        -t ${ECR_REGISTRY}/${ECR_REPOSITORY}/test:${CI_COMMIT_SHA} \
        -c "cd /app/src && pytest --cov=. --cov-report=term --junitxml=/reports/junit.xml --cov-report=xml:/reports/coverage.xml ."
    - docker rm -f test-runner || true
  coverage: '/TOTAL\s+\d+\s+\d+\s+(\d+\%)/'
  artifacts:
    when: always
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
  needs: [build-test-image]

flake8-lint:
  stage: test
  rules:
    - if: $SKIP_TESTS == "true"
      when: never
    - when: on_success
  extends: .docker
  script:
    - docker rm -f flake8-linter || true
    - >
      docker run \
        --user $USER_UID:$USER_GID \
        --name flake8-linter \
        -e WORKER_TYPE=APP \
        -e DEBUG=true \
        -e DJANGO_SETTINGS_MODULE=coda.settings.test \
        --entrypoint /bin/bash \
        -v ${CI_PROJECT_DIR}:/reports \
        -t ${ECR_REGISTRY}/${ECR_REPOSITORY}/test:${CI_COMMIT_SHA} \
        -c "cd /app/src && flake8 . --exit-zero --output-file=/reports/flake8-report.txt && flake8_junit /reports/flake8-report.txt /reports/flake8_results.xml"
    - docker rm -f flake8-linter || true
  artifacts:
    when: always
    paths:
      - flake8-report.txt
      - flake8_results.xml
    reports:
      junit: flake8_results.xml
  allow_failure: true
  needs: [build-test-image]


cleanup-resources:
  stage: cleanup
  extends: .docker
  when: always
  interruptible: false
  script:
    - echo "Cleaning up resources..."
    - >
      docker rmi ${ECR_REGISTRY}/${ECR_REPOSITORY}/test:${CI_COMMIT_SHA} || true
    - docker system prune -f
    - chmod +x scripts/ecr.sh
    - sh ./scripts/ecr.sh cleanup symphony-staging/coda/test 30
    - sh ./scripts/ecr.sh cleanup symphony-staging/coda/transcription 30
    - sh ./scripts/ecr.sh cleanup symphony-staging/coda/nginx 30
    - sh ./scripts/ecr.sh cleanup symphony-staging/coda/app 30
    - sh ./scripts/ecr.sh list


build-app-images:
  stage: build
  resource_group: $CI_COMMIT_REF_NAME-lock
  extends: .docker-build
  script:
    - >
      for TARGET in nginx app transcription; do
        echo "Building $TARGET image..."
        docker buildx build --progress=plain \
          --build-arg UID=${USER_ID} \
          --build-arg GID=${GROUP_ID} \
          --target $TARGET \
          --cache-to type=local,dest=/cache/buildx-cache,mode=max \
          --cache-to type=registry,mode=max,image-manifest=true,oci-mediatypes=true,ref=${ECR_REGISTRY}/${ECR_REPOSITORY}/$TARGET:cache \
          --cache-from type=local,src=/cache/buildx-cache \
          --cache-from type=registry,ref=${ECR_REGISTRY}/${ECR_REPOSITORY}/$TARGET:cache \
          -t ${ECR_REGISTRY}/${ECR_REPOSITORY}/$TARGET:${CI_COMMIT_SHA} \
          --push \
          .
          echo "Finished building $TARGET image."
      done
  only:
    - dev


.deployment:
  extends: .base
  resource_group: $CI_COMMIT_REF_NAME-lock
  image:
    name: alpine:3.19

staging-deployment:
  extends: .deployment
  stage: deploy-staging
  before_script:
    - apk add -q --no-cache openssh-client aws-cli
    - mkdir -p ~/.ssh
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\tUserKnownHostsFile /dev/null\n" > ~/.ssh/config
    - chmod 600 ~/.ssh/config
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | base64 -d | tr -d '\r' | ssh-add -
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
  script:
    - |
      ROLLBACK_COMMIT_SHA=$(ssh alexandros@************ "
        sudo docker inspect --format='{{.Config.Image}}' ${ADMIN_CONTAINER_NAME} | awk -F: '{print $2}' 2>/dev/null || echo 'none'
      ")
    - echo "ROLLBACK_COMMIT_SHA=${ROLLBACK_COMMIT_SHA}" >> deployment.env
    # Define host-container mapping
    - HOSTS="************ ************ ************ ************"
    - CONTAINERS_217="${NGINX_CONTAINER_NAME} ${ADMIN_CONTAINER_NAME} ${FLOWER_CONTAINER_NAME}"
    - CONTAINERS_230="${MESSENGER_CONTAINER_NAME} ${DEFAULTWORKER_CONTAINER_NAME}"
    - CONTAINERS_210="${TRANSCRIPTION_CONTAINER_NAME}"
    - CONTAINERS_197="${LLMGENERICAPI_CONTAINER_NAME}"
    - >
      for HOST in $HOSTS; do
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_217"; fi
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_230"; fi
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_210"; fi
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_197"; fi
        echo "Copying deployment script to $HOST ..."
        scp ./scripts/coda.sh alexandros@$HOST:/home/<USER>
        echo "Stopping in $HOST: $CONTAINERS"
        ssh alexandros@"$HOST" "
          sudo chmod +x /home/<USER>/coda.sh
          for CONTAINER in ${CONTAINERS}; do
            sudo /home/<USER>/coda.sh stop \$CONTAINER --remove
          done
        "
      done
    - echo "Applying migrations"
    - >
      ssh alexandros@************ "
        sudo /home/<USER>/coda.sh run ${CI_COMMIT_SHA} migrate --no-migration-check --log-folder /var/log/coda_logs
        sudo /home/<USER>/coda.sh cleanup
      "
    - echo "Finished migrations"
    - >
      for HOST in $HOSTS; do
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_217"; fi
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_230"; fi
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_210"; fi
        if [ "$HOST" = "************" ]; then CONTAINERS="$CONTAINERS_197"; fi
        echo "Deploying in $HOST: $CONTAINERS"
        ssh alexandros@"$HOST" "
          for CONTAINER in ${CONTAINERS}; do
            sudo /home/<USER>/coda.sh run ${CI_COMMIT_SHA} \$CONTAINER -d --no-migration-check --log-folder /var/log/coda_logs
          done
          sudo /home/<USER>/coda.sh cleanup
        "
      done
  artifacts:
    reports:
      dotenv: deployment.env
  only:
    - dev
  allow_failure: true
  needs: [build-app-images]


staging-test-deployment:
  extends: .deployment
  stage: test-staging
  before_script:
    - apk add -q --no-cache openssh-client aws-cli
    - mkdir -p ~/.ssh
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\tUserKnownHostsFile /dev/null\n" > ~/.ssh/config
    - chmod 600 ~/.ssh/config
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | base64 -d | tr -d '\r' | ssh-add -
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
#    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
  script:
    - echo "ROLLBACK_COMMIT_SHA ${ROLLBACK_COMMIT_SHA}"
    - echo "We should now test if rollback is required... TO DO :)"
  only:
    - dev
  needs:
    - job: staging-deployment
      artifacts: true

#    - scp ./scripts/coda.sh alexandros@************:/home/<USER>
#    - scp ./scripts/coda.sh alexandros@************:/home/<USER>
#    - scp ./scripts/coda.sh alexandros@************:/home/<USER>
#    - scp ./scripts/coda.sh alexandros@************:/home/<USER>
#    - >
#      for TARGET in "************"; do
#        echo "Stopping CODA on '$TARGET' ..."
#        ssh alexandros@"$TARGET" "
#          sudo chmod +x /home/<USER>/coda.sh
#          for CONTAINER in ${ADMIN_CONTAINER_NAME} ${MESSENGER_CONTAINER_NAME} ${DEFAULTWORKER_CONTAINER_NAME} ${FLOWER_CONTAINER_NAME} ${NGINX_CONTAINER_NAME} ${TRANSCRIPTION_CONTAINER_NAME} ${LLMGENERIC_CONTAINER_NAME}; do
#            sudo /home/<USER>/coda.sh stop \$CONTAINER --remove
#          for CONTAINER in ${ADMIN_CONTAINER_NAME} ${FLOWER_CONTAINER_NAME}; do
#            sudo /home/<USER>/coda.sh run ${CI_COMMIT_SHA} \$CONTAINER -d --log-folder /var/log/coda_logs
#          done
#        "
#      done


#          for CONTAINER in ${ADMIN_CONTAINER_NAME} ${MESSENGER_CONTAINER_NAME} ${DEFAULTWORKER_CONTAINER_NAME} ${FLOWER_CONTAINER_NAME} ${NGINX_CONTAINER_NAME} ${TRANSCRIPTION_CONTAINER_NAME} ${LLMGENERIC_CONTAINER_NAME}; do
#    - |
#      ssh alexandros@************ "
#        # Store current image for potential rollback
#        CURRENT_IMAGE=\$(sudo docker inspect --format='{{.Config.Image}}' ${ADMIN_CONTAINER_NAME} 2>/dev/null || echo 'none')
#        echo \"Current image: \$CURRENT_IMAGE\"
#
#        # Login to ECR
#        aws ecr get-login-password --region us-east-1 | sudo docker login --username AWS --password-stdin ${ECR_REGISTRY}
#
#        # Stop and remove current container
#        sudo docker stop ${ADMIN_CONTAINER_NAME} || true
#        sudo docker rm ${ADMIN_CONTAINER_NAME} || true
#
#        # Run new container
#        sudo docker run --name ${ADMIN_CONTAINER_NAME} --restart unless-stopped -d \
#          --user ${USER_ID}:${GROUP_ID} \
#          --network host -p ${EXTERNAL_PORT}:3000 \
#          -v /var/log/umonyarchive_logs:/app/logs \
#          -e ${ESTATE_API_URL} \
#          ${ECR_REGISTRY}/${ECR_REPOSITORY}:${CI_COMMIT_SHA}
#
#        # Wait for application to be ready
#        timeout=180
#        interval=5
#        end=\$((SECONDS+timeout))
#        while [ \$SECONDS -lt \$end ]; do
#          if curl -o /dev/null -s http://localhost:${EXTERNAL_PORT}; then
#            echo 'Application is up and running'
#            exit 0
#          fi
#          sleep \$interval
#        done
#
#        # If we get here, deployment failed
#        echo 'Application failed to start within the timeout period'
#
#        # Attempt rollback if we had a previous container
#        if [ \"\$CURRENT_IMAGE\" != \"none\" ]; then
#          echo 'Rolling back to previous version'
#          sudo docker stop ${ADMIN_CONTAINER_NAME} || true
#          sudo docker rm ${ADMIN_CONTAINER_NAME} || true
#          sudo docker run --name ${ADMIN_CONTAINER_NAME} --restart unless-stopped -d \
#            --user ${USER_ID}:${GROUP_ID} \
#            --network host -p ${EXTERNAL_PORT}:3000 \
#            -v /var/log/umonyarchive_logs:/app/logs \
#            -e ${ESTATE_API_URL} \
#            \$CURRENT_IMAGE
#        fi
#
#        exit 1
#      "
#  environment:
#    name: staging
#    url: http://$SSH_HOST:${EXTERNAL_PORT}

